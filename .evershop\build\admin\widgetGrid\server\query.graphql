{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsobj: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsobk: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsobl: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsobm: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsobn: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsobo: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsobp: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsobq: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsobr: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsobs: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsobt: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsobu: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsobv: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsobw: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsobx: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsoby: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsobz: version\n", "eddbb2e31a83a0001a7564db4316ad5bb": "\n  e3cb8f7smcvvsoc0: widgets(filters: $variable_3cb8f7smcvvsoc2) {\n    items {\n      widgetId\n      uuid\n      name\n      area\n      route\n      type\n      status\n      editUrl\n      updateApi\n      deleteApi\n    }\n    total\n    currentFilters {\n      key\n      operation\n      value\n    }\n  }\n  e3cb8f7smcvvsoc1: widgetTypes {\n    code\n    name\n  }\n", "e87e95e5276f8c2cbeec9f2ac985d59ca": "", "e9014d94fa690307e34750ab0c8aa5173": "\n  e3cb8f7smcvvsoc3: widgetTypes {\n    code\n    name\n    description\n    createWidgetUrl\n  }\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsoc4: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsoc5: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsoc6: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsoc7: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsoc8: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsoc9: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsoca: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsocb: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsocc: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsocd\n    count: $variable_3cb8f7smcvvsoce\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsocf: textWidget(text: $variable_3cb8f7smcvvsock, className: $variable_3cb8f7smcvvsocl) {\n    text\n    className\n  }\n  e3cb8f7smcvvsocg: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoch: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoci: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsocj: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsocm: basicMenuWidget(settings: $variable_3cb8f7smcvvsocn) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "eddbb2e31a83a0001a7564db4316ad5bb": {"values": {"variable_3cb8f7smcvvsoc2": "getContextValue_J2ZpbHRlcnNGcm9tVXJsJw=="}, "defs": [{"origin": "filters", "type": "[FilterInput]", "alias": "variable_3cb8f7smcvvsoc2"}]}, "e87e95e5276f8c2cbeec9f2ac985d59ca": {"values": {}, "defs": []}, "e9014d94fa690307e34750ab0c8aa5173": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsocd": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsoce": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsocd"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsoce"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsock": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsocl": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsock"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsocl"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsocn": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsocn"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsobj"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsobk"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsobl"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsobm"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsobn"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsobo"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsobp"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsobq"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsobr"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsobs"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsobt"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsobu"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsobv"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsobw"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsobx"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsoby"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsobz"}], "eddbb2e31a83a0001a7564db4316ad5bb": [{"origin": "widgets", "alias": "e3cb8f7smcvvsoc0"}, {"origin": "widgetTypes", "alias": "e3cb8f7smcvvsoc1"}], "e87e95e5276f8c2cbeec9f2ac985d59ca": [], "e9014d94fa690307e34750ab0c8aa5173": [{"origin": "widgetTypes", "alias": "e3cb8f7smcvvsoc3"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsoc4"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsoc5"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsoc6"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsoc7"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsoc8"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsoc9"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsoca"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsocb"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsocc"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsocf"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsocg"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsoch"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsoci"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsocj"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsocm"}]}}