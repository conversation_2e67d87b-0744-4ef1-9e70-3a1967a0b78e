
      import React from 'react';
      import ReactDOM from 'react-dom';
      import { Area } from '@evershop/evershop/components/common';
      import {HydrateAdmin} from '@evershop/evershop/components/common';
      
import ec70c22df5f099bdfcebe397859226054 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/auth/pages/admin/all/AdminUser.js';
import e97b100f517bff553aae97dd31d7f9249 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/admin/all/CatalogMenuGroup.js';
import e04b8121a7250abb640811c2476af1150 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/admin/all/NewProductQuickLink.js';
import eb76f74151daf1b93f9edbe45b86f26e5 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/admin/all/ShippingSettingMenu.js';
import e5d7dc0c8c76e45451e795f8d743fb527 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/CmsMenuGroup.js';
import e08490b3c06c2de7f3c690854f0a1bed6 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/CopyRight.js';
import eb1a516897eae4329796fdfd9457bf2a2 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Layout.js';
import e03bcf91a570563c8710aaeff437938d1 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Logo.js';
import ee6d31560871c3e93cb8b02d93e5c9ed2 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Meta.js';
import e8f7ea183df71b7a7285cc0a8dc8f6b60 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Navigation.js';
import e7e3bfab6f7542419f684826a014797ee from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Notification.js';
import e4a518579666d48f424ac549955b8600d from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/QuickLinks.js';
import e7891d52a003753856feaed4d441a5125 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/SearchBox.js';
import e9381802a22771a312927abfa56eb1f44 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/admin/all/Version.js';
import e0cb3843d9b393dccd23ba72e26af5d76 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/customer/pages/admin/all/CustomerMenuGroup.js';
import e210d82a6f26d6c09230a430f6d53658a from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/oms/pages/admin/all/OmsMenuGroup.js';
import e76e8f55114fbd34dd521f9dacfc320f5 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/promotion/pages/admin/all/CouponMenuGroup.js';
import e649537ca6fafb59dffcf59fcd8446016 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/promotion/pages/admin/all/NewCouponQuickLink.js';
import e903864b09f1dd86f890772de34a5bacd from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/setting/pages/admin/all/PaymentSettingMenu.js';
import e6b8adbe0216888a342a04f37bd1f6624 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/setting/pages/admin/all/SettingMenuGroup.js';
import e0297bc82affd261ba0c504e82a2cdd2f from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/setting/pages/admin/all/StoreSettingMenu.js';
import e7836577ca064eb03926afeba2a889b93 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/tax/pages/admin/all/TaxSettingMenu.js';
import ed4aa280d3204f4c28cde2a4cc3355de2 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/tax/pages/admin/taxSetting/TaxSetting.js';
import e361d24f0917afaaeb65f3f4088fb8348 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/extensions/sample/dist/pages/admin/all/Hello.js';
import collection_products from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/admin/widgets/CollectionProductsSetting.js';
import text_block from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/admin/widgets/TextBlockSetting.js';
import basic_menu from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/admin/widgets/BasicMenuSetting.js';
Area.defaultProps.components = {
  header: {
    ec70c22df5f099bdfcebe397859226054: {
      id: 'ec70c22df5f099bdfcebe397859226054',
      sortOrder: 50,
      component: { default: ec70c22df5f099bdfcebe397859226054 }
    },
    e03bcf91a570563c8710aaeff437938d1: {
      id: 'e03bcf91a570563c8710aaeff437938d1',
      sortOrder: 10,
      component: { default: e03bcf91a570563c8710aaeff437938d1 }
    },
    e7891d52a003753856feaed4d441a5125: {
      id: 'e7891d52a003753856feaed4d441a5125',
      sortOrder: 20,
      component: { default: e7891d52a003753856feaed4d441a5125 }
    }
  },
  adminMenu: {
    e97b100f517bff553aae97dd31d7f9249: {
      id: 'e97b100f517bff553aae97dd31d7f9249',
      sortOrder: 20,
      component: { default: e97b100f517bff553aae97dd31d7f9249 }
    },
    e5d7dc0c8c76e45451e795f8d743fb527: {
      id: 'e5d7dc0c8c76e45451e795f8d743fb527',
      sortOrder: 60,
      component: { default: e5d7dc0c8c76e45451e795f8d743fb527 }
    },
    e4a518579666d48f424ac549955b8600d: {
      id: 'e4a518579666d48f424ac549955b8600d',
      sortOrder: 10,
      component: { default: e4a518579666d48f424ac549955b8600d }
    },
    e0cb3843d9b393dccd23ba72e26af5d76: {
      id: 'e0cb3843d9b393dccd23ba72e26af5d76',
      sortOrder: 40,
      component: { default: e0cb3843d9b393dccd23ba72e26af5d76 }
    },
    e210d82a6f26d6c09230a430f6d53658a: {
      id: 'e210d82a6f26d6c09230a430f6d53658a',
      sortOrder: 30,
      component: { default: e210d82a6f26d6c09230a430f6d53658a }
    },
    e76e8f55114fbd34dd521f9dacfc320f5: {
      id: 'e76e8f55114fbd34dd521f9dacfc320f5',
      sortOrder: 50,
      component: { default: e76e8f55114fbd34dd521f9dacfc320f5 }
    },
    e6b8adbe0216888a342a04f37bd1f6624: {
      id: 'e6b8adbe0216888a342a04f37bd1f6624',
      sortOrder: 500,
      component: { default: e6b8adbe0216888a342a04f37bd1f6624 }
    }
  },
  quickLinks: {
    e04b8121a7250abb640811c2476af1150: {
      id: 'e04b8121a7250abb640811c2476af1150',
      sortOrder: 20,
      component: { default: e04b8121a7250abb640811c2476af1150 }
    },
    e649537ca6fafb59dffcf59fcd8446016: {
      id: 'e649537ca6fafb59dffcf59fcd8446016',
      sortOrder: 30,
      component: { default: e649537ca6fafb59dffcf59fcd8446016 }
    }
  },
  settingPageMenu: {
    eb76f74151daf1b93f9edbe45b86f26e5: {
      id: 'eb76f74151daf1b93f9edbe45b86f26e5',
      sortOrder: 15,
      component: { default: eb76f74151daf1b93f9edbe45b86f26e5 }
    },
    e903864b09f1dd86f890772de34a5bacd: {
      id: 'e903864b09f1dd86f890772de34a5bacd',
      sortOrder: 10,
      component: { default: e903864b09f1dd86f890772de34a5bacd }
    },
    e0297bc82affd261ba0c504e82a2cdd2f: {
      id: 'e0297bc82affd261ba0c504e82a2cdd2f',
      sortOrder: 5,
      component: { default: e0297bc82affd261ba0c504e82a2cdd2f }
    },
    e7836577ca064eb03926afeba2a889b93: {
      id: 'e7836577ca064eb03926afeba2a889b93',
      sortOrder: 20,
      component: { default: e7836577ca064eb03926afeba2a889b93 }
    }
  },
  footerLeft: {
    e08490b3c06c2de7f3c690854f0a1bed6: {
      id: 'e08490b3c06c2de7f3c690854f0a1bed6',
      sortOrder: 10,
      component: { default: e08490b3c06c2de7f3c690854f0a1bed6 }
    },
    e9381802a22771a312927abfa56eb1f44: {
      id: 'e9381802a22771a312927abfa56eb1f44',
      sortOrder: 20,
      component: { default: e9381802a22771a312927abfa56eb1f44 }
    }
  },
  body: {
    eb1a516897eae4329796fdfd9457bf2a2: {
      id: 'eb1a516897eae4329796fdfd9457bf2a2',
      sortOrder: 10,
      component: { default: eb1a516897eae4329796fdfd9457bf2a2 }
    },
    e7e3bfab6f7542419f684826a014797ee: {
      id: 'e7e3bfab6f7542419f684826a014797ee',
      sortOrder: 10,
      component: { default: e7e3bfab6f7542419f684826a014797ee }
    }
  },
  head: {
    ee6d31560871c3e93cb8b02d93e5c9ed2: {
      id: 'ee6d31560871c3e93cb8b02d93e5c9ed2',
      sortOrder: 5,
      component: { default: ee6d31560871c3e93cb8b02d93e5c9ed2 }
    }
  },
  adminNavigation: {
    e8f7ea183df71b7a7285cc0a8dc8f6b60: {
      id: 'e8f7ea183df71b7a7285cc0a8dc8f6b60',
      sortOrder: 10,
      component: { default: e8f7ea183df71b7a7285cc0a8dc8f6b60 }
    }
  },
  content: {
    ed4aa280d3204f4c28cde2a4cc3355de2: {
      id: 'ed4aa280d3204f4c28cde2a4cc3355de2',
      sortOrder: 10,
      component: { default: ed4aa280d3204f4c28cde2a4cc3355de2 }
    },
    e361d24f0917afaaeb65f3f4088fb8348: {
      id: 'e361d24f0917afaaeb65f3f4088fb8348',
      sortOrder: 0,
      component: { default: e361d24f0917afaaeb65f3f4088fb8348 }
    }
  },
  '*': {
    collection_products: {
      id: 'collection_products',
      sortOrder: 0,
      component: { default: collection_products }
    },
    text_block: {
      id: 'text_block',
      sortOrder: 0,
      component: { default: text_block }
    },
    basic_menu: {
      id: 'basic_menu',
      sortOrder: 0,
      component: { default: basic_menu }
    }
  }
} 
ReactDOM.hydrate(
        React.createElement(HydrateAdmin, null),
        document.getElementById('app')
      );