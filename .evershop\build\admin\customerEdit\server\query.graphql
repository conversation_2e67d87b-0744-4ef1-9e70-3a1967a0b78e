{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsp1g: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsp1h: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsp1i: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsp1j: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsp1k: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsp1l: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsp1m: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsp1n: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsp1o: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsp1p: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsp1q: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsp1r: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsp1s: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsp1t: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsp1u: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsp1v: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsp1w: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsp1x: url(routeId: \"customerGrid\")\n", "ed2dbb1aa9b12d9d617e9c0b2faf3a557": "\n  e3cb8f7smcvvsp1y: url(routeId: \"customerGrid\")\n", "e8ccbe9cafd2142372c57233f58d490dd": "\n  e3cb8f7smcvvsp1z: customer(id: \"getContextValue_ImN1c3RvbWVyVXVpZCIsIG51bGw=\") {\n    customerId\n    fullName\n    email\n    status\n    group {\n      groupName\n    }\n  }\n", "ebbaa89a6dc9f2a0da30ccad294004884": "\n  e3cb8f7smcvvsp20: customer(id: \"getContextValue_ImN1c3RvbWVyVXVpZCIsIG51bGw=\") {\n    orders {\n      orderNumber\n      uuid\n      editUrl\n      createdAt {\n        text\n      }\n      shipmentStatus {\n        name\n      }\n      paymentStatus {\n        name\n      }\n      grandTotal {\n        text\n      }\n    }\n  }\n", "e5f1a15abf5b8ee3e74693df2ec929512": "\n  e3cb8f7smcvvsp21: customer(id: \"getContextValue_ImN1c3RvbWVyVXVpZCIsIG51bGw=\") {\n    fullName\n  }\n  e3cb8f7smcvvsp22: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsp23: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsp24: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsp25: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsp26: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsp27: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsp28: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsp29: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsp2a: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsp2b\n    count: $variable_3cb8f7smcvvsp2c\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsp2d: textWidget(text: $variable_3cb8f7smcvvsp2i, className: $variable_3cb8f7smcvvsp2j) {\n    text\n    className\n  }\n  e3cb8f7smcvvsp2e: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp2f: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp2g: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp2h: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsp2k: basicMenuWidget(settings: $variable_3cb8f7smcvvsp2l) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "ed2dbb1aa9b12d9d617e9c0b2faf3a557": {"values": {}, "defs": []}, "e8ccbe9cafd2142372c57233f58d490dd": {"values": {}, "defs": []}, "ebbaa89a6dc9f2a0da30ccad294004884": {"values": {}, "defs": []}, "e5f1a15abf5b8ee3e74693df2ec929512": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsp2b": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsp2c": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsp2b"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsp2c"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsp2i": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsp2j": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsp2i"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsp2j"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsp2l": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsp2l"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsp1g"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsp1h"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsp1i"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsp1j"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsp1k"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsp1l"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsp1m"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsp1n"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsp1o"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsp1p"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsp1q"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsp1r"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsp1s"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsp1t"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsp1u"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsp1v"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsp1w"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsp1x"}], "ed2dbb1aa9b12d9d617e9c0b2faf3a557": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsp1y"}], "e8ccbe9cafd2142372c57233f58d490dd": [{"origin": "customer", "alias": "e3cb8f7smcvvsp1z"}], "ebbaa89a6dc9f2a0da30ccad294004884": [{"origin": "customer", "alias": "e3cb8f7smcvvsp20"}], "e5f1a15abf5b8ee3e74693df2ec929512": [{"origin": "customer", "alias": "e3cb8f7smcvvsp21"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsp22"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsp23"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsp24"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsp25"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsp26"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsp27"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsp28"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsp29"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsp2a"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsp2d"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsp2e"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsp2f"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsp2g"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsp2h"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsp2k"}]}}