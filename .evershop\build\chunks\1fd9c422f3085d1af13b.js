"use strict";(self.webpackChunkglow_254=self.webpackChunkglow_254||[]).push([[9601],{9601:(e,o,i)=>{let n,r,s,l,a,c,d,h,p,u,f,g,m;i.d(o,{default:()=>ir});try{if("u">typeof document){var b=document.createElement("style");b.appendChild(document.createTextNode(".ce-hint--align-start{text-align:left}.ce-hint--align-center{text-align:center}.ce-hint__description{opacity:.6;margin-top:3px}")),document.head.appendChild(b)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}var v="u">typeof globalThis?globalThis:"u">typeof window?window:"u">typeof global?global:"u">typeof self?self:{};function k(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function y(){}Object.assign(y,{default:y,register:y,revert:function(){},__esModule:!0}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){let o=(this.document||this.ownerDocument).querySelectorAll(e),i=o.length;for(;--i>=0&&o.item(i)!==this;);return i>-1}),Element.prototype.closest||(Element.prototype.closest=function(e){let o=this;if(!document.documentElement.contains(o))return null;do{if(o.matches(e))return o;o=o.parentElement||o.parentNode}while(null!==o);return null}),Element.prototype.prepend||(Element.prototype.prepend=function(e){let o=document.createDocumentFragment();Array.isArray(e)||(e=[e]),e.forEach(e=>{let i=e instanceof Node;o.appendChild(i?e:document.createTextNode(e))}),this.insertBefore(o,this.firstChild)}),Element.prototype.scrollIntoViewIfNeeded||(Element.prototype.scrollIntoViewIfNeeded=function(e){e=0==arguments.length||!!e;let o=this.parentNode,i=window.getComputedStyle(o,null),n=parseInt(i.getPropertyValue("border-top-width")),r=parseInt(i.getPropertyValue("border-left-width")),s=this.offsetTop-o.offsetTop<o.scrollTop,l=this.offsetTop-o.offsetTop+this.clientHeight-n>o.scrollTop+o.clientHeight,a=this.offsetLeft-o.offsetLeft<o.scrollLeft,c=this.offsetLeft-o.offsetLeft+this.clientWidth-r>o.scrollLeft+o.clientWidth;(s||l)&&e&&(o.scrollTop=this.offsetTop-o.offsetTop-o.clientHeight/2-n+this.clientHeight/2),(a||c)&&e&&(o.scrollLeft=this.offsetLeft-o.offsetLeft-o.clientWidth/2-r+this.clientWidth/2),(s||l||a||c)&&!e&&this.scrollIntoView(s&&!l)}),window.requestIdleCallback=window.requestIdleCallback||function(e){let o=Date.now();return setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-o))}})},1)},window.cancelIdleCallback=window.cancelIdleCallback||function(e){clearTimeout(e)};var x=((n=x||{}).VERBOSE="VERBOSE",n.INFO="INFO",n.WARN="WARN",n.ERROR="ERROR",n);function w(e,o,i="log",n,r="color: inherit"){if(!("console"in window)||!window.console[i])return;let s=["info","log","warn","error"].includes(i),l=[];switch(w.logLevel){case"ERROR":if("error"!==i)return;break;case"WARN":if(!["error","warn"].includes(i))return;break;case"INFO":if(!s||e)return}n&&l.push(n);let a="Editor.js 2.31.0-rc.7",c=`line-height: 1em;
            color: #006FEA;
            display: inline-block;
            font-size: 11px;
            line-height: 1em;
            background-color: #fff;
            padding: 4px 9px;
            border-radius: 30px;
            border: 1px solid rgba(56, 138, 229, 0.16);
            margin: 4px 5px 4px 0;`;e&&(s?(l.unshift(c,r),o=`%c${a}%c ${o}`):o=`( ${a} )${o}`);try{s?n?console[i](`${o} %o`,...l):console[i](o,...l):console[i](o)}catch{}}w.logLevel="VERBOSE";let E=w.bind(window,!1),C=w.bind(window,!0);function B(e){return Object.prototype.toString.call(e).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function T(e){return"function"===B(e)||"asyncfunction"===B(e)}function S(e){return"object"===B(e)}function I(e){return"string"===B(e)}function O(e){return"number"===B(e)}function _(e){return"undefined"===B(e)}function M(e){return!e||0===Object.keys(e).length&&e.constructor===Object}function A(e){return e>47&&e<58||32===e||13===e||229===e||e>64&&e<91||e>95&&e<112||e>185&&e<193||e>218&&e<223}async function L(e,o=()=>{},i=()=>{}){async function n(e,o,i){try{await e.function(e.data),await o(_(e.data)?{}:e.data)}catch{i(_(e.data)?{}:e.data)}}return e.reduce(async(e,r)=>(await e,n(r,o,i)),Promise.resolve())}function P(e){return Array.prototype.slice.call(e)}function N(e,o){return function(){let i=this,n=arguments;window.setTimeout(()=>e.apply(i,n),o)}}function D(e,o,i){let n;return(...r)=>{let s=this,l=i&&!n;window.clearTimeout(n),n=window.setTimeout(()=>{n=null,i||e.apply(s,r)},o),l&&e.apply(s,r)}}function R(e,o,i){let n,r,s,l=null,a=0;i||(i={});let c=function(){a=!1===i.leading?0:Date.now(),l=null,s=e.apply(n,r),l||(n=r=null)};return function(){let d=Date.now();a||!1!==i.leading||(a=d);let h=o-(d-a);return n=this,r=arguments,h<=0||h>o?(l&&(clearTimeout(l),l=null),a=d,s=e.apply(n,r),l||(n=r=null)):l||!1===i.trailing||(l=setTimeout(c,h)),s}}function j(e){return e[0].toUpperCase()+e.slice(1)}function F(e){let o=function(){let e={win:!1,mac:!1,x11:!1,linux:!1},o=Object.keys(e).find(e=>-1!==window.navigator.appVersion.toLowerCase().indexOf(e));return o&&(e[o]=!0),e}();return e=e.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi," + "),e=o.mac?e.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):e.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN")}function H(e,o,i){let n=`\xab${o}\xbb is deprecated and will be removed in the next major release. Please use the \xab${i}\xbb instead.`;e&&C(n,"warn")}function z(e,o,i){let n=i.value?"value":"get",r=i[n],s=`#${o}Cache`;if(i[n]=function(...e){return void 0===this[s]&&(this[s]=r.apply(this,...e)),this[s]},"get"===n&&i.set){let o=i.set;i.set=function(i){delete e[s],o.apply(this,i)}}return i}function U(){return window.matchMedia("(max-width: 650px)").matches}let $="u">typeof window&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&window.navigator.maxTouchPoints>1);class Y{static isSingleTag(e){return e.tagName&&["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(e.tagName)}static isLineBreakTag(e){return e&&e.tagName&&["BR","WBR"].includes(e.tagName)}static make(e,o=null,i={}){let n=document.createElement(e);if(Array.isArray(o)){let e=o.filter(e=>void 0!==e);n.classList.add(...e)}else o&&n.classList.add(o);for(let e in i)Object.prototype.hasOwnProperty.call(i,e)&&(n[e]=i[e]);return n}static text(e){return document.createTextNode(e)}static append(e,o){Array.isArray(o)?o.forEach(o=>e.appendChild(o)):e.appendChild(o)}static prepend(e,o){Array.isArray(o)?(o=o.reverse()).forEach(o=>e.prepend(o)):e.prepend(o)}static swap(e,o){let i=document.createElement("div"),n=e.parentNode;n.insertBefore(i,e),n.insertBefore(e,o),n.insertBefore(o,i),n.removeChild(i)}static find(e=document,o){return e.querySelector(o)}static get(e){return document.getElementById(e)}static findAll(e=document,o){return e.querySelectorAll(o)}static get allInputsSelector(){return"[contenteditable=true], textarea, input:not([type]), "+["text","password","email","number","search","tel","url"].map(e=>`input[type="${e}"]`).join(", ")}static findAllInputs(e){return P(e.querySelectorAll(Y.allInputsSelector)).reduce((e,o)=>Y.isNativeInput(o)||Y.containsOnlyInlineElements(o)?[...e,o]:[...e,...Y.getDeepestBlockElements(o)],[])}static getDeepestNode(e,o=!1){let i=o?"lastChild":"firstChild",n=o?"previousSibling":"nextSibling";if(e&&e.nodeType===Node.ELEMENT_NODE&&e[i]){let r=e[i];if(Y.isSingleTag(r)&&!Y.isNativeInput(r)&&!Y.isLineBreakTag(r))if(r[n])r=r[n];else{if(!r.parentNode[n])return r.parentNode;r=r.parentNode[n]}return this.getDeepestNode(r,o)}return e}static isElement(e){return!O(e)&&e&&e.nodeType&&e.nodeType===Node.ELEMENT_NODE}static isFragment(e){return!O(e)&&e&&e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE}static isContentEditable(e){return"true"===e.contentEditable}static isNativeInput(e){return!!e&&!!e.tagName&&["INPUT","TEXTAREA"].includes(e.tagName)}static canSetCaret(e){let o=!0;if(Y.isNativeInput(e))switch(e.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":o=!1}else o=Y.isContentEditable(e);return o}static isNodeEmpty(e,o){let i;return(!this.isSingleTag(e)||!!this.isLineBreakTag(e))&&(i=this.isElement(e)&&this.isNativeInput(e)?e.value:e.textContent.replace("​",""),o&&(i=i.replace(RegExp(o,"g"),"")),0===i.length)}static isLeaf(e){return!!e&&0===e.childNodes.length}static isEmpty(e,o){let i=[e];for(;i.length>0;)if(e=i.shift()){if(this.isLeaf(e)&&!this.isNodeEmpty(e,o))return!1;e.childNodes&&i.push(...Array.from(e.childNodes))}return!0}static isHTMLString(e){let o=Y.make("div");return o.innerHTML=e,o.childElementCount>0}static getContentLength(e){return Y.isNativeInput(e)?e.value.length:e.nodeType===Node.TEXT_NODE?e.length:e.textContent.length}static get blockElements(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]}static containsOnlyInlineElements(e){let o;I(e)?(o=document.createElement("div")).innerHTML=e:o=e;let i=e=>!Y.blockElements.includes(e.tagName.toLowerCase())&&Array.from(e.children).every(i);return Array.from(o.children).every(i)}static getDeepestBlockElements(e){return Y.containsOnlyInlineElements(e)?[e]:Array.from(e.children).reduce((e,o)=>[...e,...Y.getDeepestBlockElements(o)],[])}static getHolder(e){return I(e)?document.getElementById(e):e}static isAnchor(e){return"a"===e.tagName.toLowerCase()}static offset(e){let o=e.getBoundingClientRect(),i=window.pageXOffset||document.documentElement.scrollLeft,n=window.pageYOffset||document.documentElement.scrollTop,r=o.top+n,s=o.left+i;return{top:r,left:s,bottom:r+o.height,right:s+o.width}}}function W(e){e.dataset.empty=Y.isEmpty(e)?"true":"false"}let K={ui:{blockTunes:{toggler:{"Click to tune":"","or drag to move":""}},inlineToolbar:{converter:{"Convert to":""}},toolbar:{toolbox:{Add:""}},popover:{Filter:"","Nothing found":"","Convert to":""}},toolNames:{Text:"",Link:"",Bold:"",Italic:""},tools:{link:{"Add a link":""},stub:{"The block can not be displayed correctly.":""}},blockTunes:{delete:{Delete:"","Click to delete":""},moveUp:{"Move up":""},moveDown:{"Move down":""}}},X=class e{static ui(o,i){return e._t(o,i)}static t(o,i){return e._t(o,i)}static setDictionary(o){e.currentDictionary=o}static _t(o,i){let n=e.getNamespace(o);return n&&n[i]?n[i]:i}static getNamespace(o){return o.split(".").reduce((e,o)=>e&&Object.keys(e).length?e[o]:{},e.currentDictionary)}};X.currentDictionary=K;class V extends Error{}class Z{constructor(){this.subscribers={}}on(e,o){e in this.subscribers||(this.subscribers[e]=[]),this.subscribers[e].push(o)}once(e,o){e in this.subscribers||(this.subscribers[e]=[]);let i=n=>{let r=o(n),s=this.subscribers[e].indexOf(i);return -1!==s&&this.subscribers[e].splice(s,1),r};this.subscribers[e].push(i)}emit(e,o){M(this.subscribers)||!this.subscribers[e]||this.subscribers[e].reduce((e,o)=>{let i=o(e);return void 0!==i?i:e},o)}off(e,o){if(void 0===this.subscribers[e])return void console.warn(`EventDispatcher .off(): there is no subscribers for event "${e.toString()}". Probably, .off() called before .on()`);for(let i=0;i<this.subscribers[e].length;i++)if(this.subscribers[e][i]===o){delete this.subscribers[e][i];break}}destroy(){this.subscribers={}}}function q(e){Object.setPrototypeOf(this,{get id(){return e.id},get name(){return e.name},get config(){return e.config},get holder(){return e.holder},get isEmpty(){return e.isEmpty},get selected(){return e.selected},set stretched(t){e.stretched=t},get stretched(){return e.stretched},get focusable(){return e.focusable},call:(o,i)=>e.call(o,i),save:()=>e.save(),validate:o=>e.validate(o),dispatchChange(){e.dispatchChange()},getActiveToolboxEntry:()=>e.getActiveToolboxEntry()})}class G{constructor(){this.allListeners=[]}on(e,o,i,n=!1){let r=function(e=""){return`${e}${Math.floor(1e8*Math.random()).toString(16)}`}("l");if(!this.findOne(e,o,i))return this.allListeners.push({id:r,element:e,eventType:o,handler:i,options:n}),e.addEventListener(o,i,n),r}off(e,o,i,n){let r=this.findAll(e,o,i);r.forEach((e,o)=>{let i=this.allListeners.indexOf(r[o]);i>-1&&(this.allListeners.splice(i,1),e.element.removeEventListener(e.eventType,e.handler,e.options))})}offById(e){let o=this.findById(e);o&&o.element.removeEventListener(o.eventType,o.handler,o.options)}findOne(e,o,i){let n=this.findAll(e,o,i);return n.length>0?n[0]:null}findAll(e,o,i){let n=e?this.findByEventTarget(e):[];return e&&o&&i?n.filter(e=>e.eventType===o&&e.handler===i):e&&o?n.filter(e=>e.eventType===o):n}removeAll(){this.allListeners.map(e=>{e.element.removeEventListener(e.eventType,e.handler,e.options)}),this.allListeners=[]}destroy(){this.removeAll()}findByEventTarget(e){return this.allListeners.filter(o=>{if(o.element===e)return o})}findByType(e){return this.allListeners.filter(o=>{if(o.eventType===e)return o})}findByHandler(e){return this.allListeners.filter(o=>{if(o.handler===e)return o})}findById(e){return this.allListeners.find(o=>o.id===e)}}class Q{constructor({config:e,eventsDispatcher:o}){if(this.nodes={},this.listeners=new G,this.readOnlyMutableListeners={on:(e,o,i,n=!1)=>{this.mutableListenerIds.push(this.listeners.on(e,o,i,n))},clearAll:()=>{for(let e of this.mutableListenerIds)this.listeners.offById(e);this.mutableListenerIds=[]}},this.mutableListenerIds=[],new.target===Q)throw TypeError("Constructors for abstract class Module are not allowed.");this.config=e,this.eventsDispatcher=o}set state(e){this.Editor=e}removeAllNodes(){for(let e in this.nodes){let o=this.nodes[e];o instanceof HTMLElement&&o.remove()}}get isRtl(){return"rtl"===this.config.i18n.direction}}class J{constructor(){this.instance=null,this.selection=null,this.savedSelectionRange=null,this.isFakeBackgroundEnabled=!1,this.commandBackground="backColor",this.commandRemoveFormat="removeFormat"}static get CSS(){return{editorWrapper:"codex-editor",editorZone:"codex-editor__redactor"}}static get anchorNode(){let e=window.getSelection();return e?e.anchorNode:null}static get anchorElement(){let e=window.getSelection();if(!e)return null;let o=e.anchorNode;return o?Y.isElement(o)?o:o.parentElement:null}static get anchorOffset(){let e=window.getSelection();return e?e.anchorOffset:null}static get isCollapsed(){let e=window.getSelection();return e?e.isCollapsed:null}static get isAtEditor(){return this.isSelectionAtEditor(J.get())}static isSelectionAtEditor(e){if(!e)return!1;let o=e.anchorNode||e.focusNode;o&&o.nodeType===Node.TEXT_NODE&&(o=o.parentNode);let i=null;return o&&o instanceof Element&&(i=o.closest(`.${J.CSS.editorZone}`)),!!i&&i.nodeType===Node.ELEMENT_NODE}static isRangeAtEditor(e){if(!e)return;let o=e.startContainer;o&&o.nodeType===Node.TEXT_NODE&&(o=o.parentNode);let i=null;return o&&o instanceof Element&&(i=o.closest(`.${J.CSS.editorZone}`)),!!i&&i.nodeType===Node.ELEMENT_NODE}static get isSelectionExists(){return!!J.get().anchorNode}static get range(){return this.getRangeFromSelection(this.get())}static getRangeFromSelection(e){return e&&e.rangeCount?e.getRangeAt(0):null}static get rect(){let e=document.selection,o,i={x:0,y:0,width:0,height:0};if(e&&"Control"!==e.type)return o=e.createRange(),i.x=o.boundingLeft,i.y=o.boundingTop,i.width=o.boundingWidth,i.height=o.boundingHeight,i;if(!window.getSelection)return E("Method window.getSelection is not supported","warn"),i;if(null===(e=window.getSelection()).rangeCount||isNaN(e.rangeCount))return E("Method SelectionUtils.rangeCount is not supported","warn"),i;if(0===e.rangeCount)return i;if((o=e.getRangeAt(0).cloneRange()).getBoundingClientRect&&(i=o.getBoundingClientRect()),0===i.x&&0===i.y){let e=document.createElement("span");if(e.getBoundingClientRect){e.appendChild(document.createTextNode("​")),o.insertNode(e),i=e.getBoundingClientRect();let n=e.parentNode;n.removeChild(e),n.normalize()}}return i}static get text(){return window.getSelection?window.getSelection().toString():""}static get(){return window.getSelection()}static setCursor(e,o=0){let i=document.createRange(),n=window.getSelection();return Y.isNativeInput(e)?Y.canSetCaret(e)?(e.focus(),e.selectionStart=e.selectionEnd=o,e.getBoundingClientRect()):void 0:(i.setStart(e,o),i.setEnd(e,o),n.removeAllRanges(),n.addRange(i),i.getBoundingClientRect())}static isRangeInsideContainer(e){let o=J.range;return null!==o&&e.contains(o.startContainer)}static addFakeCursor(){let e=J.range;if(null===e)return;let o=Y.make("span","codex-editor__fake-cursor");o.dataset.mutationFree="true",e.collapse(),e.insertNode(o)}static isFakeCursorInsideContainer(e){return null!==Y.find(e,".codex-editor__fake-cursor")}static removeFakeCursor(e=document.body){let o=Y.find(e,".codex-editor__fake-cursor");o&&o.remove()}removeFakeBackground(){this.isFakeBackgroundEnabled&&(this.isFakeBackgroundEnabled=!1,document.execCommand(this.commandRemoveFormat))}setFakeBackground(){document.execCommand(this.commandBackground,!1,"#a8d6ff"),this.isFakeBackgroundEnabled=!0}save(){this.savedSelectionRange=J.range}restore(){if(!this.savedSelectionRange)return;let e=window.getSelection();e.removeAllRanges(),e.addRange(this.savedSelectionRange)}clearSaved(){this.savedSelectionRange=null}collapseToEnd(){let e=window.getSelection(),o=document.createRange();o.selectNodeContents(e.focusNode),o.collapse(!1),e.removeAllRanges(),e.addRange(o)}findParentTag(e,o,i=10){let n=window.getSelection(),r=null;return n&&n.anchorNode&&n.focusNode?([n.anchorNode,n.focusNode].forEach(n=>{let s=i;for(;s>0&&n.parentNode&&!(n.tagName===e&&(r=n,o&&n.classList&&!n.classList.contains(o)&&(r=null),r));)n=n.parentNode,s--}),r):null}expandToTag(e){let o=window.getSelection();o.removeAllRanges();let i=document.createRange();i.selectNodeContents(e),o.addRange(i)}}let ee="redactor dom changed",et="block changed",eo="fake cursor is about to be toggled",ei="fake cursor have been set",en="editor mobile layout toggled";function er(e,o){if(!e.conversionConfig)return!1;let i=e.conversionConfig[o];return T(i)||I(i)}function es(e,o){return er(e.tool,o)}function el(e,o){return Object.entries(e).some(([e,i])=>o[e]&&function(e,o){let i=Array.isArray(e)||S(e),n=Array.isArray(o)||S(o);return i||n?JSON.stringify(e)===JSON.stringify(o):e===o}(o[e],i))}async function ea(e,o){let i=(await e.save()).data,n=o.find(o=>o.name===e.name);return void 0===n||er(n,"export")?o.reduce((o,n)=>{if(!er(n,"import")||void 0===n.toolbox)return o;let r=n.toolbox.filter(o=>{if(M(o)||void 0===o.icon)return!1;if(void 0!==o.data){if(el(o.data,i))return!1}else if(n.name===e.name)return!1;return!0});return o.push({...n,toolbox:r}),o},[]):[]}function ec(e,o){return!!e.mergeable&&(e.name===o.name||es(o,"export")&&es(e,"import"))}function ed(e,o,i){let n=null==o?void 0:o.import;return T(n)?n(e,i):I(n)?{[n]:e}:(void 0!==n&&E("Conversion \xabimport\xbb property must be a string or function. String means key of tool data to import. Function accepts a imported string and return composed tool data."),{})}var eh=((r=eh||{}).Default="default",r.Separator="separator",r.Html="html",r),ep=((s=ep||{}).APPEND_CALLBACK="appendCallback",s.RENDERED="rendered",s.MOVED="moved",s.UPDATED="updated",s.REMOVED="removed",s.ON_PASTE="onPaste",s);class eu extends Z{constructor({id:e=((e=21)=>crypto.getRandomValues(new Uint8Array(e)).reduce((e,o)=>((o&=63)<36?e+=o.toString(36):o<62?e+=(o-26).toString(36).toUpperCase():o>62?e+="-":e+="_",e),""))(10),data:o,tool:i,readOnly:n,tunesData:r},s){super(),this.cachedInputs=[],this.toolRenderedElement=null,this.tunesInstances=new Map,this.defaultTunesInstances=new Map,this.unavailableTunesData={},this.inputIndex=0,this.editorEventBus=null,this.handleFocus=()=>{this.dropInputsCache(),this.updateCurrentInput()},this.didMutated=e=>{let o=void 0===e,i=e instanceof InputEvent;o||i||this.detectToolRootChange(e),(o||i||!(e.length>0&&e.every(e=>{let{addedNodes:o,removedNodes:i,target:n}=e;return[...Array.from(o),...Array.from(i),n].some(e=>(Y.isElement(e)||(e=e.parentElement),e&&null!==e.closest('[data-mutation-free="true"]')))})))&&(this.dropInputsCache(),this.updateCurrentInput(),this.toggleInputsEmptyMark(),this.call("updated"),this.emit("didMutated",this))},this.name=i.name,this.id=e,this.settings=i.settings,this.config=i.settings.config||{},this.editorEventBus=s||null,this.blockAPI=new q(this),this.tool=i,this.toolInstance=i.create(o,this.blockAPI,n),this.tunes=i.tunes,this.composeTunes(r),this.holder=this.compose(),window.requestIdleCallback(()=>{this.watchBlockMutations(),this.addInputEvents(),this.toggleInputsEmptyMark()})}static get CSS(){return{wrapper:"ce-block",wrapperStretched:"ce-block--stretched",content:"ce-block__content",selected:"ce-block--selected",dropTarget:"ce-block--drop-target"}}get inputs(){if(0!==this.cachedInputs.length)return this.cachedInputs;let e=Y.findAllInputs(this.holder);return this.inputIndex>e.length-1&&(this.inputIndex=e.length-1),this.cachedInputs=e,e}get currentInput(){return this.inputs[this.inputIndex]}set currentInput(e){let o=this.inputs.findIndex(o=>o===e||o.contains(e));-1!==o&&(this.inputIndex=o)}get firstInput(){return this.inputs[0]}get lastInput(){let e=this.inputs;return e[e.length-1]}get nextInput(){return this.inputs[this.inputIndex+1]}get previousInput(){return this.inputs[this.inputIndex-1]}get data(){return this.save().then(e=>e&&!M(e.data)?e.data:{})}get sanitize(){return this.tool.sanitizeConfig}get mergeable(){return T(this.toolInstance.merge)}get focusable(){return 0!==this.inputs.length}get isEmpty(){let e=Y.isEmpty(this.pluginsContent,"/"),o=!this.hasMedia;return e&&o}get hasMedia(){return!!this.holder.querySelector("img,iframe,video,audio,source,input,textarea,twitterwidget")}set selected(e){var o,i;this.holder.classList.toggle(eu.CSS.selected,e);let n=!0===e&&J.isRangeInsideContainer(this.holder),r=!1===e&&J.isFakeCursorInsideContainer(this.holder);(n||r)&&(null==(o=this.editorEventBus)||o.emit(eo,{state:e}),n?J.addFakeCursor():J.removeFakeCursor(this.holder),null==(i=this.editorEventBus)||i.emit(ei,{state:e}))}get selected(){return this.holder.classList.contains(eu.CSS.selected)}set stretched(e){this.holder.classList.toggle(eu.CSS.wrapperStretched,e)}get stretched(){return this.holder.classList.contains(eu.CSS.wrapperStretched)}set dropTarget(e){this.holder.classList.toggle(eu.CSS.dropTarget,e)}get pluginsContent(){return this.toolRenderedElement}call(e,o){if(T(this.toolInstance[e])){"appendCallback"===e&&E("`appendCallback` hook is deprecated and will be removed in the next major release. Use `rendered` hook instead","warn");try{this.toolInstance[e].call(this.toolInstance,o)}catch(o){E(`Error during '${e}' call: ${o.message}`,"error")}}}async mergeWith(e){await this.toolInstance.merge(e)}async save(){let e,o=await this.toolInstance.save(this.pluginsContent),i=this.unavailableTunesData;[...this.tunesInstances.entries(),...this.defaultTunesInstances.entries()].forEach(([e,o])=>{if(T(o.save))try{i[e]=o.save()}catch(e){E(`Tune ${o.constructor.name} save method throws an Error %o`,"warn",e)}});let n=window.performance.now();return Promise.resolve(o).then(o=>(e=window.performance.now(),{id:this.id,tool:this.name,data:o,tunes:i,time:e-n})).catch(e=>{E(`Saving process for ${this.name} tool failed due to the ${e}`,"log","red")})}async validate(e){let o=!0;return this.toolInstance.validate instanceof Function&&(o=await this.toolInstance.validate(e)),o}getTunes(){let e=[],o=[],i="function"==typeof this.toolInstance.renderSettings?this.toolInstance.renderSettings():[];return Y.isElement(i)?e.push({type:eh.Html,element:i}):Array.isArray(i)?e.push(...i):e.push(i),[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].map(e=>e.render()).forEach(e=>{Y.isElement(e)?o.push({type:eh.Html,element:e}):Array.isArray(e)?o.push(...e):o.push(e)}),{toolTunes:e,commonTunes:o}}updateCurrentInput(){this.currentInput=Y.isNativeInput(document.activeElement)||!J.anchorNode?document.activeElement:J.anchorNode}dispatchChange(){this.didMutated()}destroy(){this.unwatchBlockMutations(),this.removeInputEvents(),super.destroy(),T(this.toolInstance.destroy)&&this.toolInstance.destroy()}async getActiveToolboxEntry(){let e=this.tool.toolbox;if(1===e.length)return Promise.resolve(this.tool.toolbox[0]);let o=await this.data;return null==e?void 0:e.find(e=>el(e.data,o))}async exportDataAsString(){var e=await this.data,o=this.tool.conversionConfig;let i=null==o?void 0:o.export;return T(i)?i(e):I(i)?e[i]:(void 0!==i&&E("Conversion \xabexport\xbb property must be a string or function. String means key of saved data object to export. Function should export processed string to export."),"")}compose(){let e=Y.make("div",eu.CSS.wrapper),o=Y.make("div",eu.CSS.content),i=this.toolInstance.render();e.setAttribute("data-cy","block-wrapper"),e.dataset.id=this.id,this.toolRenderedElement=i,o.appendChild(this.toolRenderedElement);let n=o;return[...this.tunesInstances.values(),...this.defaultTunesInstances.values()].forEach(e=>{if(T(e.wrap))try{n=e.wrap(n)}catch(o){E(`Tune ${e.constructor.name} wrap method throws an Error %o`,"warn",o)}}),e.appendChild(n),e}composeTunes(e){Array.from(this.tunes.values()).forEach(o=>{(o.isInternal?this.defaultTunesInstances:this.tunesInstances).set(o.name,o.create(e[o.name],this.blockAPI))}),Object.entries(e).forEach(([e,o])=>{this.tunesInstances.has(e)||(this.unavailableTunesData[e]=o)})}addInputEvents(){this.inputs.forEach(e=>{e.addEventListener("focus",this.handleFocus),Y.isNativeInput(e)&&e.addEventListener("input",this.didMutated)})}removeInputEvents(){this.inputs.forEach(e=>{e.removeEventListener("focus",this.handleFocus),Y.isNativeInput(e)&&e.removeEventListener("input",this.didMutated)})}watchBlockMutations(){var e;this.redactorDomChangedCallback=e=>{let{mutations:o}=e;o.some(e=>(function(e,o){let{type:i,target:n,addedNodes:r,removedNodes:s}=e;return("attributes"!==e.type||"data-empty"!==e.attributeName)&&!!(o.contains(n)||"childList"===i&&(Array.from(r).some(e=>e===o)||Array.from(s).some(e=>e===o)))})(e,this.toolRenderedElement))&&this.didMutated(o)},null==(e=this.editorEventBus)||e.on(ee,this.redactorDomChangedCallback)}unwatchBlockMutations(){var e;null==(e=this.editorEventBus)||e.off(ee,this.redactorDomChangedCallback)}detectToolRootChange(e){e.forEach(e=>{if(Array.from(e.removedNodes).includes(this.toolRenderedElement)){let o=e.addedNodes[e.addedNodes.length-1];this.toolRenderedElement=o}})}dropInputsCache(){this.cachedInputs=[]}toggleInputsEmptyMark(){this.inputs.forEach(W)}}class ef extends Q{static getNamespace(e,o){return o?`blockTunes.${e}`:`tools.${e}`}get methods(){return{t:()=>{C("I18n.t() method can be accessed only from Tools","warn")}}}getMethodsForTool(e,o){return Object.assign(this.methods,{t:i=>X.t(ef.getNamespace(e,o),i)})}}var eg={exports:{}};window,eg.exports=function(){var e=[function(e,o,i){var n,r;i(1),n=i(6),r=null,e.exports={show:function(e){if(e.message){r||(r=n.getWrapper(),document.body.appendChild(r));var o=null,i=e.time||8e3;switch(e.type){case"confirm":o=n.confirm(e);break;case"prompt":o=n.prompt(e);break;default:o=n.alert(e),window.setTimeout(function(){o.remove()},i)}r.appendChild(o),o.classList.add("cdx-notify--bounce-in")}}}},function(e,o,i){var n=i(2);"string"==typeof n&&(n=[[e.i,n,""]]),i(4)(n,{hmr:!0,transform:void 0,insertInto:void 0}),n.locals&&(e.exports=n.locals)},function(e,o,i){(e.exports=i(3)(!1)).push([e.i,'.cdx-notify--error{background:#fffbfb!important}.cdx-notify--error::before{background:#fb5d5d!important}.cdx-notify__input{max-width:130px;padding:5px 10px;background:#f7f7f7;border:0;border-radius:3px;font-size:13px;color:#656b7c;outline:0}.cdx-notify__input:-ms-input-placeholder{color:#656b7c}.cdx-notify__input::placeholder{color:#656b7c}.cdx-notify__input:focus:-ms-input-placeholder{color:rgba(101,107,124,.3)}.cdx-notify__input:focus::placeholder{color:rgba(101,107,124,.3)}.cdx-notify__button{border:none;border-radius:3px;font-size:13px;padding:5px 10px;cursor:pointer}.cdx-notify__button:last-child{margin-left:10px}.cdx-notify__button--cancel{background:#f2f5f7;box-shadow:0 2px 1px 0 rgba(16,19,29,0);color:#656b7c}.cdx-notify__button--cancel:hover{background:#eee}.cdx-notify__button--confirm{background:#34c992;box-shadow:0 1px 1px 0 rgba(18,49,35,.05);color:#fff}.cdx-notify__button--confirm:hover{background:#33b082}.cdx-notify__btns-wrapper{display:-ms-flexbox;display:flex;-ms-flex-flow:row nowrap;flex-flow:row nowrap;margin-top:5px}.cdx-notify__cross{position:absolute;top:5px;right:5px;width:10px;height:10px;padding:5px;opacity:.54;cursor:pointer}.cdx-notify__cross::after,.cdx-notify__cross::before{content:\'\';position:absolute;left:9px;top:5px;height:12px;width:2px;background:#575d67}.cdx-notify__cross::before{transform:rotate(-45deg)}.cdx-notify__cross::after{transform:rotate(45deg)}.cdx-notify__cross:hover{opacity:1}.cdx-notifies{position:fixed;z-index:2;bottom:20px;left:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen,Ubuntu,Cantarell,"Fira Sans","Droid Sans","Helvetica Neue",sans-serif}.cdx-notify{position:relative;width:220px;margin-top:15px;padding:13px 16px;background:#fff;box-shadow:0 11px 17px 0 rgba(23,32,61,.13);border-radius:5px;font-size:14px;line-height:1.4em;word-wrap:break-word}.cdx-notify::before{content:\'\';position:absolute;display:block;top:0;left:0;width:3px;height:calc(100% - 6px);margin:3px;border-radius:5px;background:0 0}@keyframes bounceIn{0%{opacity:0;transform:scale(.3)}50%{opacity:1;transform:scale(1.05)}70%{transform:scale(.9)}100%{transform:scale(1)}}.cdx-notify--bounce-in{animation-name:bounceIn;animation-duration:.6s;animation-iteration-count:1}.cdx-notify--success{background:#fafffe!important}.cdx-notify--success::before{background:#41ffb1!important}',""])},function(e,o){e.exports=function(e){var o=[];return o.toString=function(){return this.map(function(o){var i=function(e,o){var i=e[1]||"",n=e[3];if(!n)return i;if(o&&"function"==typeof btoa){var r="/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */";return[i].concat(n.sources.map(function(e){return"/*# sourceURL="+n.sourceRoot+e+" */"})).concat([r]).join(`
`)}return[i].join(`
`)}(o,e);return o[2]?"@media "+o[2]+"{"+i+"}":i}).join("")},o.i=function(e,i){"string"==typeof e&&(e=[[null,e,""]]);for(var n={},r=0;r<this.length;r++){var s=this[r][0];"number"==typeof s&&(n[s]=!0)}for(r=0;r<e.length;r++){var l=e[r];"number"==typeof l[0]&&n[l[0]]||(i&&!l[2]?l[2]=i:i&&(l[2]="("+l[2]+") and ("+i+")"),o.push(l))}},o}},function(e,o,i){var n,r,s,l={},a=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===s&&(s=r.apply(this,arguments)),s}),c=(n={},function(e){if("function"==typeof e)return e();if(void 0===n[e]){var o=(function(e){return document.querySelector(e)}).call(this,e);if(window.HTMLIFrameElement&&o instanceof window.HTMLIFrameElement)try{o=o.contentDocument.head}catch{o=null}n[e]=o}return n[e]}),d=null,h=0,p=[],u=i(5);function f(e,o){for(var i=0;i<e.length;i++){var n=e[i],r=l[n.id];if(r){r.refs++;for(var s=0;s<r.parts.length;s++)r.parts[s](n.parts[s]);for(;s<n.parts.length;s++)r.parts.push(y(n.parts[s],o))}else{var a=[];for(s=0;s<n.parts.length;s++)a.push(y(n.parts[s],o));l[n.id]={id:n.id,refs:1,parts:a}}}}function g(e,o){for(var i=[],n={},r=0;r<e.length;r++){var s=e[r],l=o.base?s[0]+o.base:s[0],a={css:s[1],media:s[2],sourceMap:s[3]};n[l]?n[l].parts.push(a):i.push(n[l]={id:l,parts:[a]})}return i}function m(e,o){var i=c(e.insertInto);if(!i)throw Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var n=p[p.length-1];if("top"===e.insertAt)n?n.nextSibling?i.insertBefore(o,n.nextSibling):i.appendChild(o):i.insertBefore(o,i.firstChild),p.push(o);else if("bottom"===e.insertAt)i.appendChild(o);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw Error(`[Style Loader]

 Invalid value for parameter 'insertAt' ('options.insertAt') found.
 Must be 'top', 'bottom', or Object.
 (https://github.com/webpack-contrib/style-loader#insertat)
`);var r=c(e.insertInto+" "+e.insertAt.before);i.insertBefore(o,r)}}function b(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var o=p.indexOf(e);o>=0&&p.splice(o,1)}function v(e){var o=document.createElement("style");return void 0===e.attrs.type&&(e.attrs.type="text/css"),k(o,e.attrs),m(e,o),o}function k(e,o){Object.keys(o).forEach(function(i){e.setAttribute(i,o[i])})}function y(e,o){if(o.transform&&e.css){if(!(s=o.transform(e.css)))return function(){};e.css=s}if(o.singleton){var i,n,r,s,l,a=h++;n=E.bind(null,i=d||(d=v(o)),a,!1),r=E.bind(null,i,a,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(l=document.createElement("link"),void 0===o.attrs.type&&(o.attrs.type="text/css"),o.attrs.rel="stylesheet",k(l,o.attrs),m(o,l),n=(function(e,o,i){var n=i.css,r=i.sourceMap,s=void 0===o.convertToAbsoluteUrls&&r;(o.convertToAbsoluteUrls||s)&&(n=u(n)),r&&(n+=`
/*# sourceMappingURL=data:application/json;base64,`+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var l=new Blob([n],{type:"text/css"}),a=e.href;e.href=URL.createObjectURL(l),a&&URL.revokeObjectURL(a)}).bind(null,i=l,o),r=function(){b(i),i.href&&URL.revokeObjectURL(i.href)}):(n=(function(e,o){var i=o.css,n=o.media;if(n&&e.setAttribute("media",n),e.styleSheet)e.styleSheet.cssText=i;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(i))}}).bind(null,i=v(o)),r=function(){b(i)});return n(e),function(o){o?(o.css!==e.css||o.media!==e.media||o.sourceMap!==e.sourceMap)&&n(e=o):r()}}e.exports=function(e,o){if("u">typeof DEBUG&&DEBUG&&"object"!=typeof document)throw Error("The style-loader cannot be used in a non-browser environment");(o=o||{}).attrs="object"==typeof o.attrs?o.attrs:{},o.singleton||"boolean"==typeof o.singleton||(o.singleton=a()),o.insertInto||(o.insertInto="head"),o.insertAt||(o.insertAt="bottom");var i=g(e,o);return f(i,o),function(e){for(var n,r=[],s=0;s<i.length;s++){var a=i[s];(n=l[a.id]).refs--,r.push(n)}for(e&&f(g(e,o),o),s=0;s<r.length;s++)if(0===(n=r[s]).refs){for(var c=0;c<n.parts.length;c++)n.parts[c]();delete l[n.id]}}};var x,w=(x=[],function(e,o){return x[e]=o,x.filter(Boolean).join(`
`)});function E(e,o,i,n){var r=i?"":n.css;if(e.styleSheet)e.styleSheet.cssText=w(o,r);else{var s=document.createTextNode(r),l=e.childNodes;l[o]&&e.removeChild(l[o]),l.length?e.insertBefore(s,l[o]):e.appendChild(s)}}},function(e,o){e.exports=function(e){var o="u">typeof window&&window.location;if(!o)throw Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var i=o.protocol+"//"+o.host,n=i+o.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,o){var r=o.trim().replace(/^"(.*)"$/,function(e,o){return o}).replace(/^'(.*)'$/,function(e,o){return o});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(r)?e:"url("+JSON.stringify(0===r.indexOf("//")?r:0===r.indexOf("/")?i+r:n+r.replace(/^\.\//,""))+")"})}},function(e,o,i){var n,r,s;n="cdx-notify__cross",r="cdx-notify__button",e.exports={alert:s=function(e){var o=document.createElement("DIV"),i=document.createElement("DIV"),r=e.message,s=e.style;return o.classList.add("cdx-notify"),s&&o.classList.add("cdx-notify--"+s),o.innerHTML=r,i.classList.add(n),i.addEventListener("click",o.remove.bind(o)),o.appendChild(i),o},confirm:function(e){var o=s(e),i=document.createElement("div"),l=document.createElement("button"),a=document.createElement("button"),c=o.querySelector("."+n),d=e.cancelHandler,h=e.okHandler;return i.classList.add("cdx-notify__btns-wrapper"),l.innerHTML=e.okText||"Confirm",a.innerHTML=e.cancelText||"Cancel",l.classList.add(r),a.classList.add(r),l.classList.add("cdx-notify__button--confirm"),a.classList.add("cdx-notify__button--cancel"),d&&"function"==typeof d&&(a.addEventListener("click",d),c.addEventListener("click",d)),h&&"function"==typeof h&&l.addEventListener("click",h),l.addEventListener("click",o.remove.bind(o)),a.addEventListener("click",o.remove.bind(o)),i.appendChild(l),i.appendChild(a),o.appendChild(i),o},prompt:function(e){var o=s(e),i=document.createElement("div"),l=document.createElement("button"),a=document.createElement("input"),c=o.querySelector("."+n),d=e.cancelHandler,h=e.okHandler;return i.classList.add("cdx-notify__btns-wrapper"),l.innerHTML=e.okText||"Ok",l.classList.add(r),l.classList.add("cdx-notify__button--confirm"),a.classList.add("cdx-notify__input"),e.placeholder&&a.setAttribute("placeholder",e.placeholder),e.default&&(a.value=e.default),e.inputType&&(a.type=e.inputType),d&&"function"==typeof d&&c.addEventListener("click",d),h&&"function"==typeof h&&l.addEventListener("click",function(){h(a.value)}),l.addEventListener("click",o.remove.bind(o)),i.appendChild(a),i.appendChild(l),o.appendChild(i),o},getWrapper:function(){var e=document.createElement("DIV");return e.classList.add("cdx-notifies"),e}}}],o={};function i(n){if(o[n])return o[n].exports;var r=o[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=o,i.d=function(e,o,n){i.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:n})},i.r=function(e){"u">typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,o){if(1&o&&(e=i(e)),8&o||4&o&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var r in e)i.d(n,r,(function(o){return e[o]}).bind(null,r));return n},i.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(o,"a",o),o},i.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},i.p="/",i(i.s=0)}();let em=k(eg.exports);class eb{show(e){em.show(e)}}var ev={exports:{}};ev.exports=function(){function e(e){var o=e.tags;if(!Object.keys(o).map(function(e){return typeof o[e]}).every(function(e){return"object"===e||"boolean"===e||"function"===e}))throw Error("The configuration was invalid");this.config=e}var o=["P","LI","TD","TH","DIV","H1","H2","H3","H4","H5","H6","PRE"];function i(e){return -1!==o.indexOf(e.nodeName)}var n=["A","B","STRONG","I","EM","SUB","SUP","U","STRIKE"];return e.prototype.clean=function(e){let o=document.implementation.createHTMLDocument(),i=o.createElement("div");return i.innerHTML=e,this._sanitize(o,i),i.innerHTML},e.prototype._sanitize=function(e,o){var r=(l=e,a=o,l.createTreeWalker(a,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_COMMENT,null,!1)),s=r.firstChild();if(s)do{if(s.nodeType===Node.TEXT_NODE)if(!(""===s.data.trim()&&(s.previousElementSibling&&i(s.previousElementSibling)||s.nextElementSibling&&i(s.nextElementSibling))))continue;else{o.removeChild(s),this._sanitize(e,o);break}if(s.nodeType===Node.COMMENT_NODE){o.removeChild(s),this._sanitize(e,o);break}var l,a,c,d,h,p,u,f,g=(u=s,-1!==n.indexOf(u.nodeName));g&&(f=Array.prototype.some.call(s.childNodes,i));var m=!!o.parentNode,b=i(o)&&i(s)&&m,v=s.nodeName.toLowerCase(),k=(c=this.config,d=v,h=s,"function"==typeof c.tags[d]?c.tags[d](h):c.tags[d]);if(g&&f||typeof(p=k)>"u"||"boolean"==typeof p&&!p||!this.config.keepNestedBlockElements&&b){if("SCRIPT"!==s.nodeName&&"STYLE"!==s.nodeName)for(;s.childNodes.length>0;)o.insertBefore(s.childNodes[0],s);o.removeChild(s),this._sanitize(e,o);break}for(var y=0;y<s.attributes.length;y+=1){var x=s.attributes[y];(function(e,o,i){var n=e.name.toLowerCase();return!0!==o&&("function"==typeof o[n]?!o[n](e.value,i):typeof o[n]>"u"||!1===o[n]||"string"==typeof o[n]&&o[n]!==e.value)})(x,k,s)&&(s.removeAttribute(x.name),y-=1)}this._sanitize(e,s)}while(s=r.nextSibling())},e}();let ek=k(ev.exports);function ey(e,o){return e.map(e=>{let i=T(o)?o(e.tool):o;return M(i)||(e.data=function e(o,i){var n,r,s,l;return Array.isArray(o)?(n=o,r=i,n.map(o=>e(o,r))):S(o)?function(o,i){let n={};for(let s in o){var r;if(!Object.prototype.hasOwnProperty.call(o,s))continue;let l=o[s],a=S(r=i[s])||"boolean"===B(r)||T(r)?i[s]:i;n[s]=e(l,a)}return n}(o,i):I(o)?(s=o,S(l=i)?ex(s,l):!1===l?ex(s,{}):s):o}(e.data,i)),e})}function ex(e,o={}){return new ek({tags:o}).clean(e)}var ew={exports:{}};window,ew.exports=function(e){var o={};function i(n){if(o[n])return o[n].exports;var r=o[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=o,i.d=function(e,o,n){i.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:n})},i.r=function(e){"u">typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,o){if(1&o&&(e=i(e)),8&o||4&o&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var r in e)i.d(n,r,(function(o){return e[o]}).bind(null,r));return n},i.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(o,"a",o),o},i.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},i.p="",i(i.s=0)}([function(e,o,i){e.exports=i(1)},function(e,o,i){i.r(o),i.d(o,"default",function(){return n});class n{constructor(){this.nodes={wrapper:null,content:null},this.showed=!1,this.offsetTop=10,this.offsetLeft=10,this.offsetRight=10,this.hidingDelay=0,this.handleWindowScroll=()=>{this.showed&&this.hide(!0)},this.loadStyles(),this.prepare(),window.addEventListener("scroll",this.handleWindowScroll,{passive:!0})}get CSS(){return{tooltip:"ct",tooltipContent:"ct__content",tooltipShown:"ct--shown",placement:{left:"ct--left",bottom:"ct--bottom",right:"ct--right",top:"ct--top"}}}show(e,o,i){this.nodes.wrapper||this.prepare(),this.hidingTimeout&&clearTimeout(this.hidingTimeout);let n=Object.assign({placement:"bottom",marginTop:0,marginLeft:0,marginRight:0,marginBottom:0,delay:70,hidingDelay:0},i);if(n.hidingDelay&&(this.hidingDelay=n.hidingDelay),this.nodes.content.innerHTML="","string"==typeof o)this.nodes.content.appendChild(document.createTextNode(o));else{if(!(o instanceof Node))throw Error("[CodeX Tooltip] Wrong type of \xabcontent\xbb passed. It should be an instance of Node or String. But "+typeof o+" given.");this.nodes.content.appendChild(o)}switch(this.nodes.wrapper.classList.remove(...Object.values(this.CSS.placement)),n.placement){case"top":this.placeTop(e,n);break;case"left":this.placeLeft(e,n);break;case"right":this.placeRight(e,n);break;default:this.placeBottom(e,n)}n&&n.delay?this.showingTimeout=setTimeout(()=>{this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0},n.delay):(this.nodes.wrapper.classList.add(this.CSS.tooltipShown),this.showed=!0)}hide(e=!1){if(this.hidingDelay&&!e)return this.hidingTimeout&&clearTimeout(this.hidingTimeout),void(this.hidingTimeout=setTimeout(()=>{this.hide(!0)},this.hidingDelay));this.nodes.wrapper.classList.remove(this.CSS.tooltipShown),this.showed=!1,this.showingTimeout&&clearTimeout(this.showingTimeout)}onHover(e,o,i){e.addEventListener("mouseenter",()=>{this.show(e,o,i)}),e.addEventListener("mouseleave",()=>{this.hide()})}destroy(){this.nodes.wrapper.remove(),window.removeEventListener("scroll",this.handleWindowScroll)}prepare(){this.nodes.wrapper=this.make("div",this.CSS.tooltip),this.nodes.content=this.make("div",this.CSS.tooltipContent),this.append(this.nodes.wrapper,this.nodes.content),this.append(document.body,this.nodes.wrapper)}loadStyles(){let e="codex-tooltips-style";if(document.getElementById(e))return;let o=i(2),n=this.make("style",null,{textContent:o.toString(),id:e});this.prepend(document.head,n)}placeBottom(e,o){let i=e.getBoundingClientRect(),n=i.left+e.clientWidth/2-this.nodes.wrapper.offsetWidth/2,r=i.bottom+window.pageYOffset+this.offsetTop+o.marginTop;this.applyPlacement("bottom",n,r)}placeTop(e,o){let i=e.getBoundingClientRect(),n=i.left+e.clientWidth/2-this.nodes.wrapper.offsetWidth/2,r=i.top+window.pageYOffset-this.nodes.wrapper.clientHeight-this.offsetTop;this.applyPlacement("top",n,r)}placeLeft(e,o){let i=e.getBoundingClientRect(),n=i.left-this.nodes.wrapper.offsetWidth-this.offsetLeft-o.marginLeft,r=i.top+window.pageYOffset+e.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("left",n,r)}placeRight(e,o){let i=e.getBoundingClientRect(),n=i.right+this.offsetRight+o.marginRight,r=i.top+window.pageYOffset+e.clientHeight/2-this.nodes.wrapper.offsetHeight/2;this.applyPlacement("right",n,r)}applyPlacement(e,o,i){this.nodes.wrapper.classList.add(this.CSS.placement[e]),this.nodes.wrapper.style.left=o+"px",this.nodes.wrapper.style.top=i+"px"}make(e,o=null,i={}){let n=document.createElement(e);for(let e in Array.isArray(o)?n.classList.add(...o):o&&n.classList.add(o),i)i.hasOwnProperty(e)&&(n[e]=i[e]);return n}append(e,o){Array.isArray(o)?o.forEach(o=>e.appendChild(o)):e.appendChild(o)}prepend(e,o){Array.isArray(o)?(o=o.reverse()).forEach(o=>e.prepend(o)):e.prepend(o)}}},function(e,o){e.exports='.ct{z-index:999;opacity:0;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;pointer-events:none;-webkit-transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1);transition:opacity 50ms ease-in,transform 70ms cubic-bezier(.215,.61,.355,1),-webkit-transform 70ms cubic-bezier(.215,.61,.355,1);will-change:opacity,top,left;-webkit-box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);box-shadow:0 8px 12px 0 rgba(29,32,43,.17),0 4px 5px -3px rgba(5,6,12,.49);border-radius:9px}.ct,.ct:before{position:absolute;top:0;left:0}.ct:before{content:"";bottom:0;right:0;background-color:#1d202b;z-index:-1;border-radius:4px}@supports(-webkit-mask-box-image:url("")){.ct:before{border-radius:0;-webkit-mask-box-image:url(\'data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M10.71 0h2.58c3.02 0 4.64.42 6.1 1.2a8.18 8.18 0 013.4 3.4C23.6 6.07 24 7.7 24 10.71v2.58c0 3.02-.42 4.64-1.2 6.1a8.18 8.18 0 01-3.4 3.4c-1.47.8-3.1 1.21-6.11 1.21H10.7c-3.02 0-4.64-.42-6.1-1.2a8.18 8.18 0 01-3.4-3.4C.4 17.93 0 16.3 0 13.29V10.7c0-3.02.42-4.64 1.2-6.1a8.18 8.18 0 013.4-3.4C6.07.4 7.7 0 10.71 0z"/></svg>\') 48% 41% 37.9% 53.3%}}@media (--mobile){.ct{display:none}}.ct__content{padding:6px 10px;color:#cdd1e0;font-size:12px;text-align:center;letter-spacing:.02em;line-height:1em}.ct:after{content:"";width:8px;height:8px;position:absolute;background-color:#1d202b;z-index:-1}.ct--bottom{-webkit-transform:translateY(5px);transform:translateY(5px)}.ct--bottom:after{top:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--top{-webkit-transform:translateY(-5px);transform:translateY(-5px)}.ct--top:after{top:auto;bottom:-3px;left:50%;-webkit-transform:translateX(-50%) rotate(-45deg);transform:translateX(-50%) rotate(-45deg)}.ct--left{-webkit-transform:translateX(-5px);transform:translateX(-5px)}.ct--left:after{top:50%;left:auto;right:0;-webkit-transform:translate(41.6%,-50%) rotate(-45deg);transform:translate(41.6%,-50%) rotate(-45deg)}.ct--right{-webkit-transform:translateX(5px);transform:translateX(5px)}.ct--right:after{top:50%;left:0;-webkit-transform:translate(-41.6%,-50%) rotate(-45deg);transform:translate(-41.6%,-50%) rotate(-45deg)}.ct--shown{opacity:1;-webkit-transform:none;transform:none}'}]).default;let eE=k(ew.exports),eC=null;function eB(){eC||(eC=new eE)}function eT(e=!1){eB(),null==eC||eC.hide(e)}function eS(e,o,i){eB(),null==eC||eC.onHover(e,o,i)}let eI=function e(o,i){let n={};return Object.entries(o).forEach(([o,r])=>{if(S(r)){let s=i?`${i}.${o}`:o;Object.values(r).every(e=>I(e))?n[o]=s:n[o]=e(r,s);return}n[o]=r}),n}(K),eO=class e{constructor(e,o){this.cursor=-1,this.items=[],this.items=e||[],this.focusedCssClass=o}get currentItem(){return -1===this.cursor?null:this.items[this.cursor]}setCursor(e){e<this.items.length&&e>=-1&&(this.dropCursor(),this.cursor=e,this.items[this.cursor].classList.add(this.focusedCssClass))}setItems(e){this.items=e}next(){this.cursor=this.leafNodesAndReturnIndex(e.directions.RIGHT)}previous(){this.cursor=this.leafNodesAndReturnIndex(e.directions.LEFT)}dropCursor(){-1!==this.cursor&&(this.items[this.cursor].classList.remove(this.focusedCssClass),this.cursor=-1)}leafNodesAndReturnIndex(o){if(0===this.items.length)return this.cursor;let i=this.cursor;return -1===i?i=o===e.directions.RIGHT?-1:0:this.items[i].classList.remove(this.focusedCssClass),i=o===e.directions.RIGHT?(i+1)%this.items.length:(this.items.length+i-1)%this.items.length,Y.canSetCaret(this.items[i])&&N(()=>J.setCursor(this.items[i]),50)(),this.items[i].classList.add(this.focusedCssClass),i}};eO.directions={RIGHT:"right",LEFT:"left"};class e_{constructor(e){this.iterator=null,this.activated=!1,this.flipCallbacks=[],this.onKeyDown=e=>{if(this.isEventReadyForHandling(e))switch(e_.usedKeys.includes(e.keyCode)&&e.preventDefault(),e.keyCode){case 9:this.handleTabPress(e);break;case 37:case 38:this.flipLeft();break;case 39:case 40:this.flipRight();break;case 13:this.handleEnterPress(e)}},this.iterator=new eO(e.items,e.focusedItemClass),this.activateCallback=e.activateCallback,this.allowedKeys=e.allowedKeys||e_.usedKeys}get isActivated(){return this.activated}static get usedKeys(){return[9,37,39,13,38,40]}activate(e,o){this.activated=!0,e&&this.iterator.setItems(e),void 0!==o&&this.iterator.setCursor(o),document.addEventListener("keydown",this.onKeyDown,!0)}deactivate(){this.activated=!1,this.dropCursor(),document.removeEventListener("keydown",this.onKeyDown)}focusFirst(){this.dropCursor(),this.flipRight()}flipLeft(){this.iterator.previous(),this.flipCallback()}flipRight(){this.iterator.next(),this.flipCallback()}hasFocus(){return!!this.iterator.currentItem}onFlip(e){this.flipCallbacks.push(e)}removeOnFlip(e){this.flipCallbacks=this.flipCallbacks.filter(o=>o!==e)}dropCursor(){this.iterator.dropCursor()}isEventReadyForHandling(e){return this.activated&&this.allowedKeys.includes(e.keyCode)}handleTabPress(e){switch(e.shiftKey?eO.directions.LEFT:eO.directions.RIGHT){case eO.directions.RIGHT:this.flipRight();break;case eO.directions.LEFT:this.flipLeft()}}handleEnterPress(e){this.activated&&(this.iterator.currentItem&&(e.stopPropagation(),e.preventDefault(),this.iterator.currentItem.click()),T(this.activateCallback)&&this.activateCallback(this.iterator.currentItem))}flipCallback(){this.iterator.currentItem&&this.iterator.currentItem.scrollIntoViewIfNeeded(),this.flipCallbacks.forEach(e=>e())}}let eM='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7.69998 12.6L7.67896 12.62C6.53993 13.7048 6.52012 15.5155 7.63516 16.625V16.625C8.72293 17.7073 10.4799 17.7102 11.5712 16.6314L13.0263 15.193C14.0703 14.1609 14.2141 12.525 13.3662 11.3266L13.22 11.12"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16.22 11.12L16.3564 10.9805C17.2895 10.0265 17.3478 8.5207 16.4914 7.49733V7.49733C15.5691 6.39509 13.9269 6.25143 12.8271 7.17675L11.3901 8.38588C10.0935 9.47674 9.95706 11.4241 11.0888 12.6852L11.12 12.72"/></svg>',eA='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M11.5 17.5L5 11M5 11V15.5M5 11H9.5"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12.5 6.5L19 13M19 13V8.5M19 13H14.5"/></svg>';function eL(e){return(o,i)=>[[e,o].filter(e=>!!e).join("__"),i].filter(e=>!!e).join("--")}let eP=eL("ce-hint"),eN={root:eP(),alignedStart:eP(null,"align-left"),alignedCenter:eP(null,"align-center"),title:eP("title"),description:eP("description")};class eD{constructor(e){this.nodes={root:Y.make("div",[eN.root,"center"===e.alignment?eN.alignedCenter:eN.alignedStart]),title:Y.make("div",eN.title,{textContent:e.title})},this.nodes.root.appendChild(this.nodes.title),void 0!==e.description&&(this.nodes.description=Y.make("div",eN.description,{textContent:e.description}),this.nodes.root.appendChild(this.nodes.description))}getElement(){return this.nodes.root}}class eR{constructor(e){this.params=e}get name(){if(void 0!==this.params&&"name"in this.params)return this.params.name}destroy(){eT()}onChildrenOpen(){var e;void 0!==this.params&&"children"in this.params&&"function"==typeof(null==(e=this.params.children)?void 0:e.onOpen)&&this.params.children.onOpen()}onChildrenClose(){var e;void 0!==this.params&&"children"in this.params&&"function"==typeof(null==(e=this.params.children)?void 0:e.onClose)&&this.params.children.onClose()}handleClick(){var e,o;void 0!==this.params&&"onActivate"in this.params&&(null==(o=(e=this.params).onActivate)||o.call(e,this.params))}addHint(e,o){eS(e,new eD(o).getElement(),{placement:o.position,hidingDelay:100})}get children(){var e;return void 0!==this.params&&"children"in this.params&&(null==(e=this.params.children)?void 0:e.items)!==void 0?this.params.children.items:[]}get hasChildren(){return this.children.length>0}get isChildrenOpen(){var e;return void 0!==this.params&&"children"in this.params&&(null==(e=this.params.children)?void 0:e.isOpen)===!0}get isChildrenFlippable(){var e;return void 0!==this.params&&"children"in this.params&&(null==(e=this.params.children)?void 0:e.isFlippable)!==!1}get isChildrenSearchable(){var e;return void 0!==this.params&&"children"in this.params&&(null==(e=this.params.children)?void 0:e.searchable)===!0}get closeOnActivate(){return void 0!==this.params&&"closeOnActivate"in this.params&&this.params.closeOnActivate}get isActive(){return void 0!==this.params&&"isActive"in this.params&&("function"==typeof this.params.isActive?this.params.isActive():!0===this.params.isActive)}}let ej=eL("ce-popover-item"),eF={container:ej(),active:ej(null,"active"),disabled:ej(null,"disabled"),focused:ej(null,"focused"),hidden:ej(null,"hidden"),confirmationState:ej(null,"confirmation"),noHover:ej(null,"no-hover"),noFocus:ej(null,"no-focus"),title:ej("title"),secondaryTitle:ej("secondary-title"),icon:ej("icon"),iconTool:ej("icon","tool"),iconChevronRight:ej("icon","chevron-right"),wobbleAnimation:eL("wobble")()};class eH extends eR{constructor(e,o){super(e),this.params=e,this.nodes={root:null,icon:null},this.confirmationState=null,this.removeSpecialFocusBehavior=()=>{var e;null==(e=this.nodes.root)||e.classList.remove(eF.noFocus)},this.removeSpecialHoverBehavior=()=>{var e;null==(e=this.nodes.root)||e.classList.remove(eF.noHover)},this.onErrorAnimationEnd=()=>{var e,o;null==(e=this.nodes.icon)||e.classList.remove(eF.wobbleAnimation),null==(o=this.nodes.icon)||o.removeEventListener("animationend",this.onErrorAnimationEnd)},this.nodes.root=this.make(e,o)}get isDisabled(){return!0===this.params.isDisabled}get toggle(){return this.params.toggle}get title(){return this.params.title}get isConfirmationStateEnabled(){return null!==this.confirmationState}get isFocused(){return null!==this.nodes.root&&this.nodes.root.classList.contains(eF.focused)}getElement(){return this.nodes.root}handleClick(){if(this.isConfirmationStateEnabled&&null!==this.confirmationState)return void this.activateOrEnableConfirmationMode(this.confirmationState);this.activateOrEnableConfirmationMode(this.params)}toggleActive(e){var o;null==(o=this.nodes.root)||o.classList.toggle(eF.active,e)}toggleHidden(e){var o;null==(o=this.nodes.root)||o.classList.toggle(eF.hidden,e)}reset(){this.isConfirmationStateEnabled&&this.disableConfirmationMode()}onFocus(){this.disableSpecialHoverAndFocusBehavior()}make(e,o){var i,n;let r=(null==o?void 0:o.wrapperTag)||"div",s=Y.make(r,eF.container,{type:"button"===r?"button":void 0});return e.name&&(s.dataset.itemName=e.name),this.nodes.icon=Y.make("div",[eF.icon,eF.iconTool],{innerHTML:e.icon||'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="2"/></svg>'}),s.appendChild(this.nodes.icon),void 0!==e.title&&s.appendChild(Y.make("div",eF.title,{innerHTML:e.title||""})),e.secondaryLabel&&s.appendChild(Y.make("div",eF.secondaryTitle,{textContent:e.secondaryLabel})),this.hasChildren&&s.appendChild(Y.make("div",[eF.icon,eF.iconChevronRight],{innerHTML:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9.58284 17.5L14.4414 12.6414C14.5195 12.5633 14.5195 12.4367 14.4414 12.3586L9.58284 7.5"/></svg>'})),this.isActive&&s.classList.add(eF.active),e.isDisabled&&s.classList.add(eF.disabled),void 0!==e.hint&&(null==(i=null==o?void 0:o.hint)?void 0:i.enabled)!==!1&&this.addHint(s,{...e.hint,position:(null==(n=null==o?void 0:o.hint)?void 0:n.position)||"right"}),s}enableConfirmationMode(e){if(null===this.nodes.root)return;let o={...this.params,...e,confirmation:"confirmation"in e?e.confirmation:void 0},i=this.make(o);this.nodes.root.innerHTML=i.innerHTML,this.nodes.root.classList.add(eF.confirmationState),this.confirmationState=e,this.enableSpecialHoverAndFocusBehavior()}disableConfirmationMode(){if(null===this.nodes.root)return;let e=this.make(this.params);this.nodes.root.innerHTML=e.innerHTML,this.nodes.root.classList.remove(eF.confirmationState),this.confirmationState=null,this.disableSpecialHoverAndFocusBehavior()}enableSpecialHoverAndFocusBehavior(){var e,o,i;null==(e=this.nodes.root)||e.classList.add(eF.noHover),null==(o=this.nodes.root)||o.classList.add(eF.noFocus),null==(i=this.nodes.root)||i.addEventListener("mouseleave",this.removeSpecialHoverBehavior,{once:!0})}disableSpecialHoverAndFocusBehavior(){var e;this.removeSpecialFocusBehavior(),this.removeSpecialHoverBehavior(),null==(e=this.nodes.root)||e.removeEventListener("mouseleave",this.removeSpecialHoverBehavior)}activateOrEnableConfirmationMode(e){var o;if("confirmation"in e&&void 0!==e.confirmation)this.enableConfirmationMode(e.confirmation);else try{null==(o=e.onActivate)||o.call(e,e),this.disableConfirmationMode()}catch{this.animateError()}}animateError(){var e,o,i;null!=(e=this.nodes.icon)&&e.classList.contains(eF.wobbleAnimation)||(null==(o=this.nodes.icon)||o.classList.add(eF.wobbleAnimation),null==(i=this.nodes.icon)||i.addEventListener("animationend",this.onErrorAnimationEnd))}}let ez=eL("ce-popover-item-separator"),eU={container:ez(),line:ez("line"),hidden:ez(null,"hidden")};class e$ extends eR{constructor(){super(),this.nodes={root:Y.make("div",eU.container),line:Y.make("div",eU.line)},this.nodes.root.appendChild(this.nodes.line)}getElement(){return this.nodes.root}toggleHidden(e){var o;null==(o=this.nodes.root)||o.classList.toggle(eU.hidden,e)}}var eY=((l=eY||{}).Closed="closed",l.ClosedOnActivate="closed-on-activate",l);let eW=eL("ce-popover"),eK={popover:eW(),popoverContainer:eW("container"),popoverOpenTop:eW(null,"open-top"),popoverOpenLeft:eW(null,"open-left"),popoverOpened:eW(null,"opened"),search:eW("search"),nothingFoundMessage:eW("nothing-found-message"),nothingFoundMessageDisplayed:eW("nothing-found-message","displayed"),items:eW("items"),overlay:eW("overlay"),overlayHidden:eW("overlay","hidden"),popoverNested:eW(null,"nested"),getPopoverNestedClass:e=>eW(null,`nested-level-${e.toString()}`),popoverInline:eW(null,"inline"),popoverHeader:eW("header")};var eX=((a=eX||{}).NestingLevel="--nesting-level",a.PopoverHeight="--popover-height",a.InlinePopoverWidth="--inline-popover-width",a.TriggerItemLeft="--trigger-item-left",a.TriggerItemTop="--trigger-item-top",a);let eV=eL("ce-popover-item-html"),eZ={root:eV(),hidden:eV(null,"hidden")};class eq extends eR{constructor(e,o){var i,n;super(e),this.nodes={root:Y.make("div",eZ.root)},this.nodes.root.appendChild(e.element),e.name&&(this.nodes.root.dataset.itemName=e.name),void 0!==e.hint&&(null==(i=null==o?void 0:o.hint)?void 0:i.enabled)!==!1&&this.addHint(this.nodes.root,{...e.hint,position:(null==(n=null==o?void 0:o.hint)?void 0:n.position)||"right"})}getElement(){return this.nodes.root}toggleHidden(e){var o;null==(o=this.nodes.root)||o.classList.toggle(eZ.hidden,e)}getControls(){return Array.from(this.nodes.root.querySelectorAll(`button, ${Y.allInputsSelector}`))}}class eG extends Z{constructor(e,o={}){super(),this.params=e,this.itemsRenderParams=o,this.listeners=new G,this.messages={nothingFound:"Nothing found",search:"Search"},this.items=this.buildItems(e.items),e.messages&&(this.messages={...this.messages,...e.messages}),this.nodes={},this.nodes.popoverContainer=Y.make("div",[eK.popoverContainer]),this.nodes.nothingFoundMessage=Y.make("div",[eK.nothingFoundMessage],{textContent:this.messages.nothingFound}),this.nodes.popoverContainer.appendChild(this.nodes.nothingFoundMessage),this.nodes.items=Y.make("div",[eK.items]),this.items.forEach(e=>{let o=e.getElement();null!==o&&this.nodes.items.appendChild(o)}),this.nodes.popoverContainer.appendChild(this.nodes.items),this.listeners.on(this.nodes.popoverContainer,"click",e=>this.handleClick(e)),this.nodes.popover=Y.make("div",[eK.popover,this.params.class]),this.nodes.popover.appendChild(this.nodes.popoverContainer)}get itemsDefault(){return this.items.filter(e=>e instanceof eH)}getElement(){return this.nodes.popover}show(){this.nodes.popover.classList.add(eK.popoverOpened),void 0!==this.search&&this.search.focus()}hide(){this.nodes.popover.classList.remove(eK.popoverOpened),this.nodes.popover.classList.remove(eK.popoverOpenTop),this.itemsDefault.forEach(e=>e.reset()),void 0!==this.search&&this.search.clear(),this.emit(eY.Closed)}destroy(){var e;this.items.forEach(e=>e.destroy()),this.nodes.popover.remove(),this.listeners.removeAll(),null==(e=this.search)||e.destroy()}activateItemByName(e){let o=this.items.find(o=>o.name===e);this.handleItemClick(o)}buildItems(e){return e.map(e=>{switch(e.type){case eh.Separator:return new e$;case eh.Html:return new eq(e,this.itemsRenderParams[eh.Html]);default:return new eH(e,this.itemsRenderParams[eh.Default])}})}getTargetItem(e){return this.items.filter(e=>e instanceof eH||e instanceof eq).find(o=>{let i=o.getElement();return null!==i&&e.composedPath().includes(i)})}handleItemClick(e){if(!("isDisabled"in e&&e.isDisabled)){if(e.hasChildren){this.showNestedItems(e),"handleClick"in e&&"function"==typeof e.handleClick&&e.handleClick();return}this.itemsDefault.filter(o=>o!==e).forEach(e=>e.reset()),"handleClick"in e&&"function"==typeof e.handleClick&&e.handleClick(),this.toggleItemActivenessIfNeeded(e),e.closeOnActivate&&(this.hide(),this.emit(eY.ClosedOnActivate))}}handleClick(e){let o=this.getTargetItem(e);void 0!==o&&this.handleItemClick(o)}toggleItemActivenessIfNeeded(e){if(e instanceof eH&&(!0===e.toggle&&e.toggleActive(),"string"==typeof e.toggle)){let o=this.itemsDefault.filter(o=>o.toggle===e.toggle);if(1===o.length)return void e.toggleActive();o.forEach(o=>{o.toggleActive(o===e)})}}}var eQ=((c=eQ||{}).Search="search",c);let eJ=eL("cdx-search-field"),e0={wrapper:eJ(),icon:eJ("icon"),input:eJ("input")};class e1 extends Z{constructor({items:e,placeholder:o}){super(),this.listeners=new G,this.items=e,this.wrapper=Y.make("div",e0.wrapper);let i=Y.make("div",e0.icon,{innerHTML:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><circle cx="10.5" cy="10.5" r="5.5" stroke="currentColor" stroke-width="2"/><line x1="15.4142" x2="19" y1="15" y2="18.5858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>'});this.input=Y.make("input",e0.input,{placeholder:o,tabIndex:-1}),this.wrapper.appendChild(i),this.wrapper.appendChild(this.input),this.listeners.on(this.input,"input",()=>{this.searchQuery=this.input.value,this.emit(eQ.Search,{query:this.searchQuery,items:this.foundItems})})}getElement(){return this.wrapper}focus(){this.input.focus()}clear(){this.input.value="",this.searchQuery="",this.emit(eQ.Search,{query:"",items:this.foundItems})}destroy(){this.listeners.removeAll()}get foundItems(){return this.items.filter(e=>this.checkItem(e))}checkItem(e){var o,i;let n=(null==(o=e.title)?void 0:o.toLowerCase())||"",r=null==(i=this.searchQuery)?void 0:i.toLowerCase();return void 0!==r&&n.includes(r)}}var e2=Object.defineProperty,e5=Object.getOwnPropertyDescriptor;let e4=class e extends eG{constructor(e,o){super(e,o),this.nestingLevel=0,this.nestedPopoverTriggerItem=null,this.previouslyHoveredItem=null,this.scopeElement=document.body,this.hide=()=>{var e;super.hide(),this.destroyNestedPopoverIfExists(),null==(e=this.flipper)||e.deactivate(),this.previouslyHoveredItem=null},this.onFlip=()=>{let e=this.itemsDefault.find(e=>e.isFocused);null==e||e.onFocus()},this.onSearch=e=>{var o;let i=""===e.query,n=0===e.items.length;this.items.forEach(o=>{let r=!1;o instanceof eH?r=!e.items.includes(o):(o instanceof e$||o instanceof eq)&&(r=n||!i),o.toggleHidden(r)}),this.toggleNothingFoundMessage(n);let r=""===e.query?this.flippableElements:e.items.map(e=>e.getElement());null!=(o=this.flipper)&&o.isActivated&&(this.flipper.deactivate(),this.flipper.activate(r))},void 0!==e.nestingLevel&&(this.nestingLevel=e.nestingLevel),this.nestingLevel>0&&this.nodes.popover.classList.add(eK.popoverNested),void 0!==e.scopeElement&&(this.scopeElement=e.scopeElement),null!==this.nodes.popoverContainer&&this.listeners.on(this.nodes.popoverContainer,"mouseover",e=>this.handleHover(e)),e.searchable&&this.addSearch(),!1!==e.flippable&&(this.flipper=new e_({items:this.flippableElements,focusedItemClass:eF.focused,allowedKeys:[9,38,40,13]}),this.flipper.onFlip(this.onFlip))}hasFocus(){return void 0!==this.flipper&&this.flipper.hasFocus()}get scrollTop(){return null===this.nodes.items?0:this.nodes.items.scrollTop}get offsetTop(){return null===this.nodes.popoverContainer?0:this.nodes.popoverContainer.offsetTop}show(){var e;this.nodes.popover.style.setProperty(eX.PopoverHeight,this.size.height+"px"),this.shouldOpenBottom||this.nodes.popover.classList.add(eK.popoverOpenTop),this.shouldOpenRight||this.nodes.popover.classList.add(eK.popoverOpenLeft),super.show(),null==(e=this.flipper)||e.activate(this.flippableElements)}destroy(){this.hide(),super.destroy()}showNestedItems(e){null!==this.nestedPopover&&void 0!==this.nestedPopover||(this.nestedPopoverTriggerItem=e,this.showNestedPopoverForItem(e))}handleHover(e){let o=this.getTargetItem(e);void 0!==o&&this.previouslyHoveredItem!==o&&(this.destroyNestedPopoverIfExists(),this.previouslyHoveredItem=o,o.hasChildren&&this.showNestedPopoverForItem(o))}setTriggerItemPosition(e,o){let i=o.getElement(),n=(i?i.offsetTop:0)-this.scrollTop,r=this.offsetTop+n;e.style.setProperty(eX.TriggerItemTop,r+"px")}destroyNestedPopoverIfExists(){var e,o;void 0===this.nestedPopover||null===this.nestedPopover||(this.nestedPopover.off(eY.ClosedOnActivate,this.hide),this.nestedPopover.hide(),this.nestedPopover.destroy(),this.nestedPopover.getElement().remove(),this.nestedPopover=null,null==(e=this.flipper)||e.activate(this.flippableElements),null==(o=this.nestedPopoverTriggerItem)||o.onChildrenClose())}showNestedPopoverForItem(o){var i;this.nestedPopover=new e({searchable:o.isChildrenSearchable,items:o.children,nestingLevel:this.nestingLevel+1,flippable:o.isChildrenFlippable,messages:this.messages}),o.onChildrenOpen(),this.nestedPopover.on(eY.ClosedOnActivate,this.hide);let n=this.nestedPopover.getElement();return this.nodes.popover.appendChild(n),this.setTriggerItemPosition(n,o),n.style.setProperty(eX.NestingLevel,this.nestedPopover.nestingLevel.toString()),this.nestedPopover.show(),null==(i=this.flipper)||i.deactivate(),this.nestedPopover}get shouldOpenBottom(){if(void 0===this.nodes.popover||null===this.nodes.popover)return!1;let e=this.nodes.popoverContainer.getBoundingClientRect(),o=this.scopeElement.getBoundingClientRect(),i=this.size.height,n=e.top+i,r=e.top-i,s=Math.min(window.innerHeight,o.bottom);return r<o.top||n<=s}get shouldOpenRight(){if(void 0===this.nodes.popover||null===this.nodes.popover)return!1;let e=this.nodes.popover.getBoundingClientRect(),o=this.scopeElement.getBoundingClientRect(),i=this.size.width,n=e.right+i,r=e.left-i,s=Math.min(window.innerWidth,o.right);return r<o.left||n<=s}get size(){var e;let o={height:0,width:0};if(null===this.nodes.popover)return o;let i=this.nodes.popover.cloneNode(!0);i.style.visibility="hidden",i.style.position="absolute",i.style.top="-1000px",i.classList.add(eK.popoverOpened),null==(e=i.querySelector("."+eK.popoverNested))||e.remove(),document.body.appendChild(i);let n=i.querySelector("."+eK.popoverContainer);return o.height=n.offsetHeight,o.width=n.offsetWidth,i.remove(),o}get flippableElements(){return this.items.map(e=>e instanceof eH?e.getElement():e instanceof eq?e.getControls():void 0).flat().filter(e=>null!=e)}addSearch(){this.search=new e1({items:this.itemsDefault,placeholder:this.messages.search}),this.search.on(eQ.Search,this.onSearch);let e=this.search.getElement();e.classList.add(eK.search),this.nodes.popoverContainer.insertBefore(e,this.nodes.popoverContainer.firstChild)}toggleNothingFoundMessage(e){this.nodes.nothingFoundMessage.classList.toggle(eK.nothingFoundMessageDisplayed,e)}};((e,o,i,n)=>{for(var r,s=n>1?void 0:n?e5(o,i):o,l=e.length-1;l>=0;l--)(r=e[l])&&(s=(n?r(o,i,s):r(s))||s);return n&&s&&e2(o,i,s)})([z],e4.prototype,"size",1);let e3=e4;class e6 extends e3{constructor(e){let o=!U();super({...e,class:eK.popoverInline},{[eh.Default]:{wrapperTag:"button",hint:{position:"top",alignment:"center",enabled:o}},[eh.Html]:{hint:{position:"top",alignment:"center",enabled:o}}}),this.items.forEach(e=>{(e instanceof eH||e instanceof eq)&&e.hasChildren&&e.isChildrenOpen&&this.showNestedItems(e)})}get offsetLeft(){return null===this.nodes.popoverContainer?0:this.nodes.popoverContainer.offsetLeft}show(){0===this.nestingLevel&&this.nodes.popover.style.setProperty(eX.InlinePopoverWidth,this.size.width+"px"),super.show()}handleHover(){}setTriggerItemPosition(e,o){let i=o.getElement(),n=i?i.offsetLeft:0,r=this.offsetLeft+n;e.style.setProperty(eX.TriggerItemLeft,r+"px")}showNestedItems(e){if(this.nestedPopoverTriggerItem===e){this.destroyNestedPopoverIfExists(),this.nestedPopoverTriggerItem=null;return}super.showNestedItems(e)}showNestedPopoverForItem(e){let o=super.showNestedPopoverForItem(e);return o.getElement().classList.add(eK.getPopoverNestedClass(o.nestingLevel)),o}handleItemClick(e){var o;e!==this.nestedPopoverTriggerItem&&(null==(o=this.nestedPopoverTriggerItem)||o.handleClick(),super.destroyNestedPopoverIfExists()),super.handleItemClick(e)}}let e8=class e{constructor(){this.scrollPosition=null}lock(){$?this.lockHard():document.body.classList.add(e.CSS.scrollLocked)}unlock(){$?this.unlockHard():document.body.classList.remove(e.CSS.scrollLocked)}lockHard(){this.scrollPosition=window.pageYOffset,document.documentElement.style.setProperty("--window-scroll-offset",`${this.scrollPosition}px`),document.body.classList.add(e.CSS.scrollLockedHard)}unlockHard(){document.body.classList.remove(e.CSS.scrollLockedHard),null!==this.scrollPosition&&window.scrollTo(0,this.scrollPosition),this.scrollPosition=null}};e8.CSS={scrollLocked:"ce-scroll-locked",scrollLockedHard:"ce-scroll-locked--hard"};let e7=eL("ce-popover-header"),e9={root:e7(),text:e7("text"),backButton:e7("back-button")};class te{constructor({text:e,onBackButtonClick:o}){this.listeners=new G,this.text=e,this.onBackButtonClick=o,this.nodes={root:Y.make("div",[e9.root]),backButton:Y.make("button",[e9.backButton]),text:Y.make("div",[e9.text])},this.nodes.backButton.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.5 17.5L9.64142 12.6414C9.56331 12.5633 9.56331 12.4367 9.64142 12.3586L14.5 7.5"/></svg>',this.nodes.root.appendChild(this.nodes.backButton),this.listeners.on(this.nodes.backButton,"click",this.onBackButtonClick),this.nodes.text.innerText=this.text,this.nodes.root.appendChild(this.nodes.text)}getElement(){return this.nodes.root}destroy(){this.nodes.root.remove(),this.listeners.destroy()}}class tt{constructor(){this.history=[]}push(e){this.history.push(e)}pop(){return this.history.pop()}get currentTitle(){return 0===this.history.length?"":this.history[this.history.length-1].title}get currentItems(){return 0===this.history.length?[]:this.history[this.history.length-1].items}reset(){for(;this.history.length>1;)this.pop()}}class to extends eG{constructor(e){super(e,{[eh.Default]:{hint:{enabled:!1}},[eh.Html]:{hint:{enabled:!1}}}),this.scrollLocker=new e8,this.history=new tt,this.isHidden=!0,this.nodes.overlay=Y.make("div",[eK.overlay,eK.overlayHidden]),this.nodes.popover.insertBefore(this.nodes.overlay,this.nodes.popover.firstChild),this.listeners.on(this.nodes.overlay,"click",()=>{this.hide()}),this.history.push({items:e.items})}show(){this.nodes.overlay.classList.remove(eK.overlayHidden),super.show(),this.scrollLocker.lock(),this.isHidden=!1}hide(){this.isHidden||(super.hide(),this.nodes.overlay.classList.add(eK.overlayHidden),this.scrollLocker.unlock(),this.history.reset(),this.isHidden=!0)}destroy(){super.destroy(),this.scrollLocker.unlock()}showNestedItems(e){this.updateItemsAndHeader(e.children,e.title),this.history.push({title:e.title,items:e.children})}updateItemsAndHeader(e,o){if(null!==this.header&&void 0!==this.header&&(this.header.destroy(),this.header=null),void 0!==o){this.header=new te({text:o,onBackButtonClick:()=>{this.history.pop(),this.updateItemsAndHeader(this.history.currentItems,this.history.currentTitle)}});let e=this.header.getElement();null!==e&&this.nodes.popoverContainer.insertBefore(e,this.nodes.popoverContainer.firstChild)}this.items.forEach(e=>{var o;return null==(o=e.getElement())?void 0:o.remove()}),this.items=this.buildItems(e),this.items.forEach(e=>{var o;let i=e.getElement();null!==i&&(null==(o=this.nodes.items)||o.appendChild(i))})}}var ti={exports:{}};window,ti.exports=function(e){var o={};function i(n){if(o[n])return o[n].exports;var r=o[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,i),r.l=!0,r.exports}return i.m=e,i.c=o,i.d=function(e,o,n){i.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:n})},i.r=function(e){"u">typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,o){if(1&o&&(e=i(e)),8&o||4&o&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var r in e)i.d(n,r,(function(o){return e[o]}).bind(null,r));return n},i.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(o,"a",o),o},i.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},i.p="",i(i.s=0)}([function(e,o,i){function n(e,o){for(var i=0;i<o.length;i++){var n=o[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(e,o,i){return o&&n(e.prototype,o),i&&n(e,i),e}i.r(o),o.default=function(){function e(o){var i=this;(function(e,o){if(!(e instanceof o))throw TypeError("Cannot call a class as a function")})(this,e),this.commands={},this.keys={},this.name=o.name,this.parseShortcutName(o.name),this.element=o.on,this.callback=o.callback,this.executeShortcut=function(e){i.execute(e)},this.element.addEventListener("keydown",this.executeShortcut,!1)}return r(e,null,[{key:"supportedCommands",get:function(){return{SHIFT:["SHIFT"],CMD:["CMD","CONTROL","COMMAND","WINDOWS","CTRL"],ALT:["ALT","OPTION"]}}},{key:"keyCodes",get:function(){return{0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,BACKSPACE:8,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,INSERT:45,DELETE:46,".":190}}}]),r(e,[{key:"parseShortcutName",value:function(o){o=o.split("+");for(var i=0;i<o.length;i++){o[i]=o[i].toUpperCase();var n=!1;for(var r in e.supportedCommands)if(e.supportedCommands[r].includes(o[i])){n=this.commands[r]=!0;break}n||(this.keys[o[i]]=!0)}for(var s in e.supportedCommands)this.commands[s]||(this.commands[s]=!1)}},{key:"execute",value:function(o){var i,n={CMD:o.ctrlKey||o.metaKey,SHIFT:o.shiftKey,ALT:o.altKey},r=!0;for(i in this.commands)this.commands[i]!==n[i]&&(r=!1);var s,l=!0;for(s in this.keys)l=l&&o.keyCode===e.keyCodes[s];r&&l&&this.callback(o)}},{key:"remove",value:function(){this.element.removeEventListener("keydown",this.executeShortcut)}}]),e}()}]).default;let tn=k(ti.exports),tr=new class{constructor(){this.registeredShortcuts=new Map}add(e){if(this.findShortcut(e.on,e.name))throw Error(`Shortcut ${e.name} is already registered for ${e.on}. Please remove it before add a new handler.`);let o=new tn({name:e.name,on:e.on,callback:e.handler}),i=this.registeredShortcuts.get(e.on)||[];this.registeredShortcuts.set(e.on,[...i,o])}remove(e,o){let i=this.findShortcut(e,o);if(!i)return;i.remove();let n=this.registeredShortcuts.get(e);this.registeredShortcuts.set(e,n.filter(e=>e!==i))}findShortcut(e,o){return(this.registeredShortcuts.get(e)||[]).find(({name:e})=>e===o)}};var ts=Object.defineProperty,tl=Object.getOwnPropertyDescriptor,ta=(e,o,i,n)=>{for(var r,s=n>1?void 0:n?tl(o,i):o,l=e.length-1;l>=0;l--)(r=e[l])&&(s=(n?r(o,i,s):r(s))||s);return n&&s&&ts(o,i,s),s},tc=((d=tc||{}).Opened="toolbox-opened",d.Closed="toolbox-closed",d.BlockAdded="toolbox-block-added",d);let td=class e extends Z{constructor({api:o,tools:i,i18nLabels:n}){super(),this.opened=!1,this.listeners=new G,this.popover=null,this.handleMobileLayoutToggle=()=>{this.destroyPopover(),this.initPopover()},this.onPopoverClose=()=>{this.opened=!1,this.emit("toolbox-closed")},this.api=o,this.tools=i,this.i18nLabels=n,this.enableShortcuts(),this.nodes={toolbox:Y.make("div",e.CSS.toolbox)},this.initPopover(),this.nodes.toolbox.setAttribute("data-cy","toolbox"),this.api.events.on(en,this.handleMobileLayoutToggle)}get isEmpty(){return 0===this.toolsToBeDisplayed.length}static get CSS(){return{toolbox:"ce-toolbox"}}getElement(){return this.nodes.toolbox}hasFocus(){if(null!==this.popover)return"hasFocus"in this.popover?this.popover.hasFocus():void 0}destroy(){var e;super.destroy(),this.nodes&&this.nodes.toolbox&&this.nodes.toolbox.remove(),this.removeAllShortcuts(),null==(e=this.popover)||e.off(eY.Closed,this.onPopoverClose),this.listeners.destroy(),this.api.events.off(en,this.handleMobileLayoutToggle)}toolButtonActivated(e,o){this.insertNewBlock(e,o)}open(){var e;this.isEmpty||(null==(e=this.popover)||e.show(),this.opened=!0,this.emit("toolbox-opened"))}close(){var e;null==(e=this.popover)||e.hide(),this.opened=!1,this.emit("toolbox-closed")}toggle(){this.opened?this.close():this.open()}initPopover(){var e;let o=U()?to:e3;this.popover=new o({scopeElement:this.api.ui.nodes.redactor,searchable:!0,messages:{nothingFound:this.i18nLabels.nothingFound,search:this.i18nLabels.filter},items:this.toolboxItemsToBeDisplayed}),this.popover.on(eY.Closed,this.onPopoverClose),null==(e=this.nodes.toolbox)||e.append(this.popover.getElement())}destroyPopover(){null!==this.popover&&(this.popover.hide(),this.popover.off(eY.Closed,this.onPopoverClose),this.popover.destroy(),this.popover=null),null!==this.nodes.toolbox&&(this.nodes.toolbox.innerHTML="")}get toolsToBeDisplayed(){let e=[];return this.tools.forEach(o=>{o.toolbox&&e.push(o)}),e}get toolboxItemsToBeDisplayed(){let e=(e,o,i=!0)=>({icon:e.icon,title:X.t(eI.toolNames,e.title||j(o.name)),name:o.name,onActivate:()=>{this.toolButtonActivated(o.name,e.data)},secondaryLabel:o.shortcut&&i?F(o.shortcut):""});return this.toolsToBeDisplayed.reduce((o,i)=>(Array.isArray(i.toolbox)?i.toolbox.forEach((n,r)=>{o.push(e(n,i,0===r))}):void 0!==i.toolbox&&o.push(e(i.toolbox,i)),o),[])}enableShortcuts(){this.toolsToBeDisplayed.forEach(e=>{let o=e.shortcut;o&&this.enableShortcutForTool(e.name,o)})}enableShortcutForTool(e,o){tr.add({name:o,on:this.api.ui.nodes.redactor,handler:async o=>{o.preventDefault();let i=this.api.blocks.getCurrentBlockIndex(),n=this.api.blocks.getBlockByIndex(i);if(n)try{let o=await this.api.blocks.convert(n.id,e);this.api.caret.setToBlock(o,"end");return}catch{}this.insertNewBlock(e)}})}removeAllShortcuts(){this.toolsToBeDisplayed.forEach(e=>{let o=e.shortcut;o&&tr.remove(this.api.ui.nodes.redactor,o)})}async insertNewBlock(e,o){let i,n=this.api.blocks.getCurrentBlockIndex(),r=this.api.blocks.getBlockByIndex(n);if(!r)return;let s=r.isEmpty?n:n+1;o&&(i=Object.assign(await this.api.blocks.composeBlockData(e),o));let l=this.api.blocks.insert(e,i,void 0,s,void 0,r.isEmpty);l.call(ep.APPEND_CALLBACK),this.api.caret.setToBlock(s),this.emit("toolbox-block-added",{block:l}),this.api.toolbar.close()}};ta([z],td.prototype,"toolsToBeDisplayed",1),ta([z],td.prototype,"toolboxItemsToBeDisplayed",1);let th="block hovered";async function tp(e,o){let i=navigator.keyboard;if(!i)return o;try{return(await i.getLayoutMap()).get(e)||o}catch(e){return console.error(e),o}}var tu=((h=tu||{})[h.Block=0]="Block",h[h.Inline=1]="Inline",h[h.Tune=2]="Tune",h),tf=((p=tf||{}).Shortcut="shortcut",p.Toolbox="toolbox",p.EnabledInlineTools="inlineToolbar",p.EnabledBlockTunes="tunes",p.Config="config",p),tg=((u=tg||{}).Shortcut="shortcut",u.SanitizeConfig="sanitize",u),tm=((f=tm||{}).IsEnabledLineBreaks="enableLineBreaks",f.Toolbox="toolbox",f.ConversionConfig="conversionConfig",f.IsReadOnlySupported="isReadOnlySupported",f.PasteConfig="pasteConfig",f),tb=((g=tb||{}).IsInline="isInline",g.Title="title",g.IsReadOnlySupported="isReadOnlySupported",g),tv=((m=tv||{}).IsTune="isTune",m);class tk{constructor({name:e,constructable:o,config:i,api:n,isDefault:r,isInternal:s=!1,defaultPlaceholder:l}){this.api=n,this.name=e,this.constructable=o,this.config=i,this.isDefault=r,this.isInternal=s,this.defaultPlaceholder=l}get settings(){let e=this.config.config||{};return!this.isDefault||"placeholder"in e||!this.defaultPlaceholder||(e.placeholder=this.defaultPlaceholder),e}reset(){if(T(this.constructable.reset))return this.constructable.reset()}prepare(){if(T(this.constructable.prepare))return this.constructable.prepare({toolName:this.name,config:this.settings})}get shortcut(){let e=this.constructable.shortcut;return this.config.shortcut||e}get sanitizeConfig(){return this.constructable.sanitize||{}}isInline(){return this.type===tu.Inline}isBlock(){return this.type===tu.Block}isTune(){return this.type===tu.Tune}}function ty(){let e=window.getSelection();if(null===e)return[null,0];let o=e.focusNode,i=e.focusOffset;return null===o?[null,0]:(o.nodeType!==Node.TEXT_NODE&&o.childNodes.length>0&&(o.childNodes[i]?(o=o.childNodes[i],i=0):i=(o=o.childNodes[i-1]).textContent.length),[o,i])}function tx(e,o,i,n){var r;let s=document.createRange();"left"===n?(s.setStart(e,0),s.setEnd(o,i)):(s.setStart(o,i),s.setEnd(e,e.childNodes.length));let l=s.cloneContents(),a=document.createElement("div");return a.appendChild(l),r=a.textContent||"",!/[^\t\n\r ]/.test(r)}function tw(e){let o=Y.getDeepestNode(e);if(null===o||Y.isEmpty(e))return!0;if(Y.isNativeInput(o))return 0===o.selectionEnd;if(Y.isEmpty(e))return!0;let[i,n]=ty();return null!==i&&tx(e,i,n,"left")}function tE(e){let o=Y.getDeepestNode(e,!0);if(null===o)return!0;if(Y.isNativeInput(o))return o.selectionEnd===o.value.length;let[i,n]=ty();return null!==i&&tx(e,i,n,"right")}var tC={},tB={},tT={},tS={},tI={},tO={};Object.defineProperty(tO,"__esModule",{value:!0}),tO.allInputsSelector=function(){return"[contenteditable=true], textarea, input:not([type]), "+["text","password","email","number","search","tel","url"].map(function(e){return'input[type="'.concat(e,'"]')}).join(", ")},Object.defineProperty(tI,"__esModule",{value:!0}),tI.allInputsSelector=void 0,Object.defineProperty(tI,"allInputsSelector",{enumerable:!0,get:function(){return tO.allInputsSelector}});var t_={},tM={};Object.defineProperty(tM,"__esModule",{value:!0}),tM.isNativeInput=function(e){return!!e&&!!e.tagName&&["INPUT","TEXTAREA"].includes(e.tagName)},Object.defineProperty(t_,"__esModule",{value:!0}),t_.isNativeInput=void 0,Object.defineProperty(t_,"isNativeInput",{enumerable:!0,get:function(){return tM.isNativeInput}});var tA={},tL={};Object.defineProperty(tL,"__esModule",{value:!0}),tL.append=function(e,o){Array.isArray(o)?o.forEach(function(o){e.appendChild(o)}):e.appendChild(o)},Object.defineProperty(tA,"__esModule",{value:!0}),tA.append=void 0,Object.defineProperty(tA,"append",{enumerable:!0,get:function(){return tL.append}});var tP={},tN={};Object.defineProperty(tN,"__esModule",{value:!0}),tN.blockElements=function(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]},Object.defineProperty(tP,"__esModule",{value:!0}),tP.blockElements=void 0,Object.defineProperty(tP,"blockElements",{enumerable:!0,get:function(){return tN.blockElements}});var tD={},tR={};Object.defineProperty(tR,"__esModule",{value:!0}),tR.calculateBaseline=function(e){var o=window.getComputedStyle(e),i=parseFloat(o.fontSize),n=parseFloat(o.lineHeight)||1.2*i,r=parseFloat(o.paddingTop),s=parseFloat(o.borderTopWidth);return parseFloat(o.marginTop)+s+r+(n-i)/2+.8*i},Object.defineProperty(tD,"__esModule",{value:!0}),tD.calculateBaseline=void 0,Object.defineProperty(tD,"calculateBaseline",{enumerable:!0,get:function(){return tR.calculateBaseline}});var tj={},tF={},tH={},tz={};Object.defineProperty(tz,"__esModule",{value:!0}),tz.isContentEditable=function(e){return"true"===e.contentEditable},Object.defineProperty(tH,"__esModule",{value:!0}),tH.isContentEditable=void 0,Object.defineProperty(tH,"isContentEditable",{enumerable:!0,get:function(){return tz.isContentEditable}}),Object.defineProperty(tF,"__esModule",{value:!0}),tF.canSetCaret=function(e){var o=!0;if((0,t_.isNativeInput)(e))switch(e.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":o=!1}else o=(0,tH.isContentEditable)(e);return o},Object.defineProperty(tj,"__esModule",{value:!0}),tj.canSetCaret=void 0,Object.defineProperty(tj,"canSetCaret",{enumerable:!0,get:function(){return tF.canSetCaret}});var tU={},t$={};function tY(){let e={win:!1,mac:!1,x11:!1,linux:!1},o=Object.keys(e).find(e=>-1!==window.navigator.appVersion.toLowerCase().indexOf(e));return void 0!==o&&(e[o]=!0),e}function tW(e){return null!=e&&""!==e&&("object"!=typeof e||Object.keys(e).length>0)}function tK(e){return Object.prototype.toString.call(e).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function tX(e){return"function"===tK(e)||"asyncfunction"===tK(e)}function tV(e){return"object"===tK(e)}let tZ=function(e){if(e.__esModule)return e;var o=e.default;if("function"==typeof o){var i=function e(){return this instanceof e?Reflect.construct(o,arguments,this.constructor):o.apply(this,arguments)};i.prototype=o.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(e).forEach(function(o){var n=Object.getOwnPropertyDescriptor(e,o);Object.defineProperty(i,o,n.get?n:{enumerable:!0,get:function(){return e[o]}})}),i}(Object.freeze(Object.defineProperty({__proto__:null,PromiseQueue:class{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((o,i)=>{this.completed=this.completed.then(e).then(o).catch(i)})}},beautifyShortcut:function(e){let o=tY();return e=e.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi,"+"),e=o.mac?e.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):e.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN")},cacheable:function(e,o,i){let n=void 0!==i.value?"value":"get",r=i[n],s=`#${o}Cache`;if(i[n]=function(...e){return void 0===this[s]&&(this[s]=r.apply(this,e)),this[s]},"get"===n&&i.set){let o=i.set;i.set=function(i){delete e[s],o.apply(this,i)}}return i},capitalize:function(e){return e[0].toUpperCase()+e.slice(1)},copyTextToClipboard:function(e){let o=document.createElement("div");o.style.position="absolute",o.style.left="-999px",o.style.bottom="-999px",o.innerHTML=e,document.body.appendChild(o);let i=window.getSelection(),n=document.createRange();if(n.selectNode(o),null===i)throw Error("Cannot copy text to clipboard");i.removeAllRanges(),i.addRange(n),document.execCommand("copy"),document.body.removeChild(o)},debounce:function(e,o,i){let n;return(...r)=>{let s=this,l=!0===i&&void 0!==n;window.clearTimeout(n),n=window.setTimeout(()=>{n=void 0,!0!==i&&e.apply(s,r)},o),l&&e.apply(s,r)}},deepMerge:function e(o,...i){if(!i.length)return o;let n=i.shift();if(tV(o)&&tV(n))for(let i in n)tV(n[i])?(void 0===o[i]&&Object.assign(o,{[i]:{}}),e(o[i],n[i])):Object.assign(o,{[i]:n[i]});return e(o,...i)},deprecationAssert:function(e,o,i){let n=`\xab${o}\xbb is deprecated and will be removed in the next major release. Please use the \xab${i}\xbb instead.`;e&&console.warn(n)},getUserOS:tY,getValidUrl:function(e){try{return new URL(e).href}catch{}return"//"===e.substring(0,2)?window.location.protocol+e:window.location.origin+e},isBoolean:function(e){return"boolean"===tK(e)},isClass:function(e){return tX(e)&&/^\s*class\s+/.test(e.toString())},isEmpty:function(e){return!tW(e)},isFunction:tX,isIosDevice:()=>"u">typeof window&&null!==window.navigator&&tW(window.navigator.platform)&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&window.navigator.maxTouchPoints>1),isNumber:function(e){return"number"===tK(e)},isObject:tV,isPrintableKey:function(e){return e>47&&e<58||32===e||13===e||229===e||e>64&&e<91||e>95&&e<112||e>185&&e<193||e>218&&e<223},isPromise:function(e){return Promise.resolve(e)===e},isString:function(e){return"string"===tK(e)},isUndefined:function(e){return"undefined"===tK(e)},keyCodes:{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},mouseButtons:{LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4},notEmpty:tW,throttle:function(e,o,i){let n,r,s,l=null,a=0;i||(i={});let c=function(){a=!1===i.leading?0:Date.now(),l=null,s=e.apply(n,r),null===l&&(n=r=null)};return function(){let d=Date.now();a||!1!==i.leading||(a=d);let h=o-(d-a);return n=this,r=arguments,h<=0||h>o?(l&&(clearTimeout(l),l=null),a=d,s=e.apply(n,r),null===l&&(n=r=null)):l||!1===i.trailing||(l=setTimeout(c,h)),s}},typeOf:tK},Symbol.toStringTag,{value:"Module"})));Object.defineProperty(t$,"__esModule",{value:!0}),t$.containsOnlyInlineElements=function(e){(0,tZ.isString)(e)?(o=document.createElement("div")).innerHTML=e:o=e;var o,i=function(e){return!(0,tP.blockElements)().includes(e.tagName.toLowerCase())&&Array.from(e.children).every(i)};return Array.from(o.children).every(i)},Object.defineProperty(tU,"__esModule",{value:!0}),tU.containsOnlyInlineElements=void 0,Object.defineProperty(tU,"containsOnlyInlineElements",{enumerable:!0,get:function(){return t$.containsOnlyInlineElements}});var tq={},tG={},tQ={},tJ={};Object.defineProperty(tJ,"__esModule",{value:!0}),tJ.make=function(e,o,i){void 0===o&&(o=null),void 0===i&&(i={});var n,r=document.createElement(e);if(Array.isArray(o)){var s=o.filter(function(e){return void 0!==e});(n=r.classList).add.apply(n,s)}else null!==o&&r.classList.add(o);for(var l in i)Object.prototype.hasOwnProperty.call(i,l)&&(r[l]=i[l]);return r},Object.defineProperty(tQ,"__esModule",{value:!0}),tQ.make=void 0,Object.defineProperty(tQ,"make",{enumerable:!0,get:function(){return tJ.make}}),Object.defineProperty(tG,"__esModule",{value:!0}),tG.fragmentToString=function(e){var o=(0,tQ.make)("div");return o.appendChild(e),o.innerHTML},Object.defineProperty(tq,"__esModule",{value:!0}),tq.fragmentToString=void 0,Object.defineProperty(tq,"fragmentToString",{enumerable:!0,get:function(){return tG.fragmentToString}});var t0={},t1={};Object.defineProperty(t1,"__esModule",{value:!0}),t1.getContentLength=function(e){var o,i;return(0,t_.isNativeInput)(e)?e.value.length:e.nodeType===Node.TEXT_NODE?e.length:null!=(i=null==(o=e.textContent)?void 0:o.length)?i:0},Object.defineProperty(t0,"__esModule",{value:!0}),t0.getContentLength=void 0,Object.defineProperty(t0,"getContentLength",{enumerable:!0,get:function(){return t1.getContentLength}});var t2={},t5={},t4=v&&v.__spreadArray||function(e,o,i){if(i||2==arguments.length)for(var n,r=0,s=o.length;r<s;r++)!n&&r in o||(n||(n=Array.prototype.slice.call(o,0,r)),n[r]=o[r]);return e.concat(n||Array.prototype.slice.call(o))};Object.defineProperty(t5,"__esModule",{value:!0}),t5.getDeepestBlockElements=function e(o){return(0,tU.containsOnlyInlineElements)(o)?[o]:Array.from(o.children).reduce(function(o,i){return t4(t4([],o,!0),e(i),!0)},[])},Object.defineProperty(t2,"__esModule",{value:!0}),t2.getDeepestBlockElements=void 0,Object.defineProperty(t2,"getDeepestBlockElements",{enumerable:!0,get:function(){return t5.getDeepestBlockElements}});var t3={},t6={},t8={},t7={};Object.defineProperty(t7,"__esModule",{value:!0}),t7.isLineBreakTag=function(e){return["BR","WBR"].includes(e.tagName)},Object.defineProperty(t8,"__esModule",{value:!0}),t8.isLineBreakTag=void 0,Object.defineProperty(t8,"isLineBreakTag",{enumerable:!0,get:function(){return t7.isLineBreakTag}});var t9={},oe={};Object.defineProperty(oe,"__esModule",{value:!0}),oe.isSingleTag=function(e){return["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(e.tagName)},Object.defineProperty(t9,"__esModule",{value:!0}),t9.isSingleTag=void 0,Object.defineProperty(t9,"isSingleTag",{enumerable:!0,get:function(){return oe.isSingleTag}}),Object.defineProperty(t6,"__esModule",{value:!0}),t6.getDeepestNode=function e(o,i){void 0===i&&(i=!1);var n=i?"lastChild":"firstChild",r=i?"previousSibling":"nextSibling";if(o.nodeType===Node.ELEMENT_NODE&&o[n]){var s=o[n];if((0,t9.isSingleTag)(s)&&!(0,t_.isNativeInput)(s)&&!(0,t8.isLineBreakTag)(s))if(s[r])s=s[r];else{if(null===s.parentNode||!s.parentNode[r])return s.parentNode;s=s.parentNode[r]}return e(s,i)}return o},Object.defineProperty(t3,"__esModule",{value:!0}),t3.getDeepestNode=void 0,Object.defineProperty(t3,"getDeepestNode",{enumerable:!0,get:function(){return t6.getDeepestNode}});var ot={},oo={},oi=v&&v.__spreadArray||function(e,o,i){if(i||2==arguments.length)for(var n,r=0,s=o.length;r<s;r++)!n&&r in o||(n||(n=Array.prototype.slice.call(o,0,r)),n[r]=o[r]);return e.concat(n||Array.prototype.slice.call(o))};Object.defineProperty(oo,"__esModule",{value:!0}),oo.findAllInputs=function(e){return Array.from(e.querySelectorAll((0,tI.allInputsSelector)())).reduce(function(e,o){return(0,t_.isNativeInput)(o)||(0,tU.containsOnlyInlineElements)(o)?oi(oi([],e,!0),[o],!1):oi(oi([],e,!0),(0,t2.getDeepestBlockElements)(o),!0)},[])},Object.defineProperty(ot,"__esModule",{value:!0}),ot.findAllInputs=void 0,Object.defineProperty(ot,"findAllInputs",{enumerable:!0,get:function(){return oo.findAllInputs}});var on={},or={};Object.defineProperty(or,"__esModule",{value:!0}),or.isCollapsedWhitespaces=function(e){return!/[^\t\n\r ]/.test(e)},Object.defineProperty(on,"__esModule",{value:!0}),on.isCollapsedWhitespaces=void 0,Object.defineProperty(on,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return or.isCollapsedWhitespaces}});var os={},ol={};Object.defineProperty(ol,"__esModule",{value:!0}),ol.isElement=function(e){return!(0,tZ.isNumber)(e)&&!!e&&!!e.nodeType&&e.nodeType===Node.ELEMENT_NODE},Object.defineProperty(os,"__esModule",{value:!0}),os.isElement=void 0,Object.defineProperty(os,"isElement",{enumerable:!0,get:function(){return ol.isElement}});var oa={},oc={},od={},oh={};Object.defineProperty(oh,"__esModule",{value:!0}),oh.isLeaf=function(e){return null!==e&&0===e.childNodes.length},Object.defineProperty(od,"__esModule",{value:!0}),od.isLeaf=void 0,Object.defineProperty(od,"isLeaf",{enumerable:!0,get:function(){return oh.isLeaf}});var op={},ou={};Object.defineProperty(ou,"__esModule",{value:!0}),ou.isNodeEmpty=function(e,o){var i="";return(!(0,t9.isSingleTag)(e)||!!(0,t8.isLineBreakTag)(e))&&((0,os.isElement)(e)&&(0,t_.isNativeInput)(e)?i=e.value:null!==e.textContent&&(i=e.textContent.replace("​","")),void 0!==o&&(i=i.replace(RegExp(o,"g"),"")),0===i.trim().length)},Object.defineProperty(op,"__esModule",{value:!0}),op.isNodeEmpty=void 0,Object.defineProperty(op,"isNodeEmpty",{enumerable:!0,get:function(){return ou.isNodeEmpty}}),Object.defineProperty(oc,"__esModule",{value:!0}),oc.isEmpty=function(e,o){e.normalize();for(var i=[e];i.length>0;){var n=i.shift();if(n){if(e=n,(0,od.isLeaf)(e)&&!(0,op.isNodeEmpty)(e,o))return!1;i.push.apply(i,Array.from(e.childNodes))}}return!0},Object.defineProperty(oa,"__esModule",{value:!0}),oa.isEmpty=void 0,Object.defineProperty(oa,"isEmpty",{enumerable:!0,get:function(){return oc.isEmpty}});var of={},og={};Object.defineProperty(og,"__esModule",{value:!0}),og.isFragment=function(e){return!(0,tZ.isNumber)(e)&&!!e&&!!e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE},Object.defineProperty(of,"__esModule",{value:!0}),of.isFragment=void 0,Object.defineProperty(of,"isFragment",{enumerable:!0,get:function(){return og.isFragment}});var om={},ob={};Object.defineProperty(ob,"__esModule",{value:!0}),ob.isHTMLString=function(e){var o=(0,tQ.make)("div");return o.innerHTML=e,o.childElementCount>0},Object.defineProperty(om,"__esModule",{value:!0}),om.isHTMLString=void 0,Object.defineProperty(om,"isHTMLString",{enumerable:!0,get:function(){return ob.isHTMLString}});var ov={},ok={};Object.defineProperty(ok,"__esModule",{value:!0}),ok.offset=function(e){var o=e.getBoundingClientRect(),i=window.pageXOffset||document.documentElement.scrollLeft,n=window.pageYOffset||document.documentElement.scrollTop,r=o.top+n,s=o.left+i;return{top:r,left:s,bottom:r+o.height,right:s+o.width}},Object.defineProperty(ov,"__esModule",{value:!0}),ov.offset=void 0,Object.defineProperty(ov,"offset",{enumerable:!0,get:function(){return ok.offset}});var oy={},ox={};Object.defineProperty(ox,"__esModule",{value:!0}),ox.prepend=function(e,o){Array.isArray(o)?(o=o.reverse()).forEach(function(o){return e.prepend(o)}):e.prepend(o)},Object.defineProperty(oy,"__esModule",{value:!0}),oy.prepend=void 0,Object.defineProperty(oy,"prepend",{enumerable:!0,get:function(){return ox.prepend}}),Object.defineProperty(tS,"__esModule",{value:!0}),tS.prepend=tS.offset=tS.make=tS.isLineBreakTag=tS.isSingleTag=tS.isNodeEmpty=tS.isLeaf=tS.isHTMLString=tS.isFragment=tS.isEmpty=tS.isElement=tS.isContentEditable=tS.isCollapsedWhitespaces=tS.findAllInputs=tS.isNativeInput=tS.allInputsSelector=tS.getDeepestNode=tS.getDeepestBlockElements=tS.getContentLength=tS.fragmentToString=tS.containsOnlyInlineElements=tS.canSetCaret=tS.calculateBaseline=tS.blockElements=tS.append=void 0,Object.defineProperty(tS,"allInputsSelector",{enumerable:!0,get:function(){return tI.allInputsSelector}}),Object.defineProperty(tS,"isNativeInput",{enumerable:!0,get:function(){return t_.isNativeInput}}),Object.defineProperty(tS,"append",{enumerable:!0,get:function(){return tA.append}}),Object.defineProperty(tS,"blockElements",{enumerable:!0,get:function(){return tP.blockElements}}),Object.defineProperty(tS,"calculateBaseline",{enumerable:!0,get:function(){return tD.calculateBaseline}}),Object.defineProperty(tS,"canSetCaret",{enumerable:!0,get:function(){return tj.canSetCaret}}),Object.defineProperty(tS,"containsOnlyInlineElements",{enumerable:!0,get:function(){return tU.containsOnlyInlineElements}}),Object.defineProperty(tS,"fragmentToString",{enumerable:!0,get:function(){return tq.fragmentToString}}),Object.defineProperty(tS,"getContentLength",{enumerable:!0,get:function(){return t0.getContentLength}}),Object.defineProperty(tS,"getDeepestBlockElements",{enumerable:!0,get:function(){return t2.getDeepestBlockElements}}),Object.defineProperty(tS,"getDeepestNode",{enumerable:!0,get:function(){return t3.getDeepestNode}}),Object.defineProperty(tS,"findAllInputs",{enumerable:!0,get:function(){return ot.findAllInputs}}),Object.defineProperty(tS,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return on.isCollapsedWhitespaces}}),Object.defineProperty(tS,"isContentEditable",{enumerable:!0,get:function(){return tH.isContentEditable}}),Object.defineProperty(tS,"isElement",{enumerable:!0,get:function(){return os.isElement}}),Object.defineProperty(tS,"isEmpty",{enumerable:!0,get:function(){return oa.isEmpty}}),Object.defineProperty(tS,"isFragment",{enumerable:!0,get:function(){return of.isFragment}}),Object.defineProperty(tS,"isHTMLString",{enumerable:!0,get:function(){return om.isHTMLString}}),Object.defineProperty(tS,"isLeaf",{enumerable:!0,get:function(){return od.isLeaf}}),Object.defineProperty(tS,"isNodeEmpty",{enumerable:!0,get:function(){return op.isNodeEmpty}}),Object.defineProperty(tS,"isLineBreakTag",{enumerable:!0,get:function(){return t8.isLineBreakTag}}),Object.defineProperty(tS,"isSingleTag",{enumerable:!0,get:function(){return t9.isSingleTag}}),Object.defineProperty(tS,"make",{enumerable:!0,get:function(){return tQ.make}}),Object.defineProperty(tS,"offset",{enumerable:!0,get:function(){return ov.offset}}),Object.defineProperty(tS,"prepend",{enumerable:!0,get:function(){return oy.prepend}});var ow={};Object.defineProperty(ow,"__esModule",{value:!0}),ow.getContenteditableSlice=function(e,o,i,n,r){void 0===r&&(r=!1);var s,l=document.createRange();if("left"===n?(l.setStart(e,0),l.setEnd(o,i)):(l.setStart(o,i),l.setEnd(e,e.childNodes.length)),!0===r){var a=l.extractContents();return(0,tS.fragmentToString)(a)}var c=l.cloneContents(),d=document.createElement("div");return d.appendChild(c),null!=(s=d.textContent)?s:""},Object.defineProperty(tT,"__esModule",{value:!0}),tT.checkContenteditableSliceForEmptiness=function(e,o,i,n){var r=(0,ow.getContenteditableSlice)(e,o,i,n);return(0,tS.isCollapsedWhitespaces)(r)},Object.defineProperty(tB,"__esModule",{value:!0}),tB.checkContenteditableSliceForEmptiness=void 0,Object.defineProperty(tB,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return tT.checkContenteditableSliceForEmptiness}});var oE={};Object.defineProperty(oE,"__esModule",{value:!0}),oE.getContenteditableSlice=void 0,Object.defineProperty(oE,"getContenteditableSlice",{enumerable:!0,get:function(){return ow.getContenteditableSlice}});var oC={},oB={};Object.defineProperty(oB,"__esModule",{value:!0}),oB.focus=function(e,o){var i,n;if(void 0===o&&(o=!0),(0,tS.isNativeInput)(e)){e.focus();var r=o?0:e.value.length;e.setSelectionRange(r,r)}else{var s=document.createRange(),l=window.getSelection();if(!l)return;var a=function(e){var o=document.createTextNode("");e.appendChild(o),s.setStart(o,0),s.setEnd(o,0)},c=function(e){return null!=e},d=e.childNodes,h=o?d[0]:d[d.length-1];if(c(h)){for(;c(h)&&h.nodeType!==Node.TEXT_NODE;)h=o?h.firstChild:h.lastChild;if(c(h)&&h.nodeType===Node.TEXT_NODE){var p=null!=(n=null==(i=h.textContent)?void 0:i.length)?n:0,r=o?0:p;s.setStart(h,r),s.setEnd(h,r)}else a(e)}else a(e);l.removeAllRanges(),l.addRange(s)}},Object.defineProperty(oC,"__esModule",{value:!0}),oC.focus=void 0,Object.defineProperty(oC,"focus",{enumerable:!0,get:function(){return oB.focus}});var oT={},oS={};Object.defineProperty(oS,"__esModule",{value:!0}),oS.getCaretNodeAndOffset=function(){var e=window.getSelection();if(null===e)return[null,0];var o=e.focusNode,i=e.focusOffset;return null===o?[null,0]:(o.nodeType!==Node.TEXT_NODE&&o.childNodes.length>0&&(void 0!==o.childNodes[i]?(o=o.childNodes[i],i=0):null!==(o=o.childNodes[i-1]).textContent&&(i=o.textContent.length)),[o,i])},Object.defineProperty(oT,"__esModule",{value:!0}),oT.getCaretNodeAndOffset=void 0,Object.defineProperty(oT,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return oS.getCaretNodeAndOffset}});var oI={},oO={};Object.defineProperty(oO,"__esModule",{value:!0}),oO.getRange=function(){var e=window.getSelection();return e&&e.rangeCount?e.getRangeAt(0):null},Object.defineProperty(oI,"__esModule",{value:!0}),oI.getRange=void 0,Object.defineProperty(oI,"getRange",{enumerable:!0,get:function(){return oO.getRange}});var o_={},oM={};Object.defineProperty(oM,"__esModule",{value:!0}),oM.isCaretAtEndOfInput=function(e){var o=(0,tS.getDeepestNode)(e,!0);if(null===o)return!0;if((0,tS.isNativeInput)(o))return o.selectionEnd===o.value.length;var i=(0,oT.getCaretNodeAndOffset)(),n=i[0],r=i[1];return null!==n&&(0,tB.checkContenteditableSliceForEmptiness)(e,n,r,"right")},Object.defineProperty(o_,"__esModule",{value:!0}),o_.isCaretAtEndOfInput=void 0,Object.defineProperty(o_,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return oM.isCaretAtEndOfInput}});var oA={},oL={};Object.defineProperty(oL,"__esModule",{value:!0}),oL.isCaretAtStartOfInput=function(e){var o=(0,tS.getDeepestNode)(e);if(null===o||(0,tS.isEmpty)(e))return!0;if((0,tS.isNativeInput)(o))return 0===o.selectionEnd;if((0,tS.isEmpty)(e))return!0;var i=(0,oS.getCaretNodeAndOffset)(),n=i[0],r=i[1];return null!==n&&(0,tT.checkContenteditableSliceForEmptiness)(e,n,r,"left")},Object.defineProperty(oA,"__esModule",{value:!0}),oA.isCaretAtStartOfInput=void 0,Object.defineProperty(oA,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return oL.isCaretAtStartOfInput}});var oP={},oN={};Object.defineProperty(oN,"__esModule",{value:!0}),oN.save=function(){var e=(0,oO.getRange)(),o=(0,tS.make)("span");if(o.id="cursor",o.hidden=!0,e)return e.insertNode(o),function(){var i=window.getSelection();i&&(e.setStartAfter(o),e.setEndAfter(o),i.removeAllRanges(),i.addRange(e),setTimeout(function(){o.remove()},150))}},Object.defineProperty(oP,"__esModule",{value:!0}),oP.save=void 0,Object.defineProperty(oP,"save",{enumerable:!0,get:function(){return oN.save}}),Object.defineProperty(tC,"__esModule",{value:!0}),tC.save=tC.isCaretAtStartOfInput=tC.isCaretAtEndOfInput=tC.getRange=tC.getCaretNodeAndOffset=tC.focus=tC.getContenteditableSlice=tC.checkContenteditableSliceForEmptiness=void 0,Object.defineProperty(tC,"checkContenteditableSliceForEmptiness",{enumerable:!0,get:function(){return tB.checkContenteditableSliceForEmptiness}}),Object.defineProperty(tC,"getContenteditableSlice",{enumerable:!0,get:function(){return oE.getContenteditableSlice}}),Object.defineProperty(tC,"focus",{enumerable:!0,get:function(){return oC.focus}}),Object.defineProperty(tC,"getCaretNodeAndOffset",{enumerable:!0,get:function(){return oT.getCaretNodeAndOffset}}),Object.defineProperty(tC,"getRange",{enumerable:!0,get:function(){return oI.getRange}}),Object.defineProperty(tC,"isCaretAtEndOfInput",{enumerable:!0,get:function(){return o_.isCaretAtEndOfInput}}),Object.defineProperty(tC,"isCaretAtStartOfInput",{enumerable:!0,get:function(){return oA.isCaretAtStartOfInput}}),Object.defineProperty(tC,"save",{enumerable:!0,get:function(){return oP.save}});class oD{constructor(e){this.blocks=[],this.workingArea=e}get length(){return this.blocks.length}get array(){return this.blocks}get nodes(){return P(this.workingArea.children)}static set(e,o,i){return isNaN(Number(o))?Reflect.set(e,o,i):e.insert(+o,i),!0}static get(e,o){return isNaN(Number(o))?Reflect.get(e,o):e.get(+o)}push(e){this.blocks.push(e),this.insertToDOM(e)}swap(e,o){let i=this.blocks[o];Y.swap(this.blocks[e].holder,i.holder),this.blocks[o]=this.blocks[e],this.blocks[e]=i}move(e,o){let i=this.blocks.splice(o,1)[0],n=Math.max(0,e-1),r=this.blocks[n];e>0?this.insertToDOM(i,"afterend",r):this.insertToDOM(i,"beforebegin",r),this.blocks.splice(e,0,i);let s=this.composeBlockEvent("move",{fromIndex:o,toIndex:e});i.call(ep.MOVED,s)}insert(e,o,i=!1){if(!this.length)return void this.push(o);if(e>this.length&&(e=this.length),i&&(this.blocks[e].holder.remove(),this.blocks[e].call(ep.REMOVED)),this.blocks.splice(e,+!!i,o),e>0){let i=this.blocks[e-1];this.insertToDOM(o,"afterend",i)}else{let i=this.blocks[e+1];i?this.insertToDOM(o,"beforebegin",i):this.insertToDOM(o)}}replace(e,o){if(void 0===this.blocks[e])throw Error("Incorrect index");this.blocks[e].holder.replaceWith(o.holder),this.blocks[e]=o}insertMany(e,o){let i=new DocumentFragment;for(let o of e)i.appendChild(o.holder);if(this.length>0){if(o>0){let e=Math.min(o-1,this.length-1);this.blocks[e].holder.after(i)}else 0===o&&this.workingArea.prepend(i);this.blocks.splice(o,0,...e)}else this.blocks.push(...e),this.workingArea.appendChild(i);e.forEach(e=>e.call(ep.RENDERED))}remove(e){isNaN(e)&&(e=this.length-1),this.blocks[e].holder.remove(),this.blocks[e].call(ep.REMOVED),this.blocks.splice(e,1)}removeAll(){this.workingArea.innerHTML="",this.blocks.forEach(e=>e.call(ep.REMOVED)),this.blocks.length=0}insertAfter(e,o){let i=this.blocks.indexOf(e);this.insert(i+1,o)}get(e){return this.blocks[e]}indexOf(e){return this.blocks.indexOf(e)}insertToDOM(e,o,i){o?i.holder.insertAdjacentElement(o,e.holder):this.workingArea.appendChild(e.holder),e.call(ep.RENDERED)}composeBlockEvent(e,o){return new CustomEvent(e,{detail:o})}}let oR="block-removed",oj="block-added",oF="block-changed";class oH{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((o,i)=>{this.completed=this.completed.then(e).then(o).catch(i)})}}class oz extends Q{get positions(){return{START:"start",END:"end",DEFAULT:"default"}}static get CSS(){return{shadowCaret:"cdx-shadow-caret"}}setToBlock(e,o=this.positions.DEFAULT,i=0){var n;let r,{BlockManager:s,BlockSelection:l}=this.Editor;if(l.clearSelection(),!e.focusable){null==(n=window.getSelection())||n.removeAllRanges(),l.selectBlock(e),s.currentBlock=e;return}switch(o){case this.positions.START:r=e.firstInput;break;case this.positions.END:r=e.lastInput;break;default:r=e.currentInput}if(!r)return;let a=Y.getDeepestNode(r,o===this.positions.END),c=Y.getContentLength(a);switch(!0){case o===this.positions.START:i=0;break;case o===this.positions.END:case i>c:i=c}this.set(a,i),s.setCurrentBlockByChildNode(e.holder),s.currentBlock.currentInput=r}setToInput(e,o=this.positions.DEFAULT,i=0){let{currentBlock:n}=this.Editor.BlockManager,r=Y.getDeepestNode(e);switch(o){case this.positions.START:this.set(r,0);break;case this.positions.END:this.set(r,Y.getContentLength(r));break;default:i&&this.set(r,i)}n.currentInput=e}set(e,o=0){let{top:i,bottom:n}=J.setCursor(e,o),{innerHeight:r}=window;i<0?window.scrollBy(0,i-30):n>r&&window.scrollBy(0,n-r+30)}setToTheLastBlock(){let e=this.Editor.BlockManager.lastBlock;if(e)if(e.tool.isDefault&&e.isEmpty)this.setToBlock(e);else{let e=this.Editor.BlockManager.insertAtEnd();this.setToBlock(e)}}extractFragmentFromCaretPosition(){let e=J.get();if(e.rangeCount){let o=e.getRangeAt(0),i=this.Editor.BlockManager.currentBlock.currentInput;if(o.deleteContents(),i)if(Y.isNativeInput(i)){let e=document.createDocumentFragment(),o=i.value.substring(0,i.selectionStart);return e.textContent=i.value.substring(i.selectionStart),i.value=o,e}else{let e=o.cloneRange();return e.selectNodeContents(i),e.setStart(o.endContainer,o.endOffset),e.extractContents()}}}navigateNext(e=!1){let{BlockManager:o}=this.Editor,{currentBlock:i,nextBlock:n}=o;if(void 0===i)return!1;let{nextInput:r,currentInput:s}=i,l=void 0!==s?tE(s):void 0,a=n,c=e||l||!i.focusable;if(r&&c)return this.setToInput(r,this.positions.START),!0;if(null===a){if(i.tool.isDefault||!c)return!1;a=o.insertAtEnd()}return!!c&&(this.setToBlock(a,this.positions.START),!0)}navigatePrevious(e=!1){let{currentBlock:o,previousBlock:i}=this.Editor.BlockManager;if(!o)return!1;let{previousInput:n,currentInput:r}=o,s=void 0!==r?tw(r):void 0,l=e||s||!o.focusable;return n&&l?(this.setToInput(n,this.positions.END),!0):null!==i&&!!l&&(this.setToBlock(i,this.positions.END),!0)}createShadow(e){let o=document.createElement("span");o.classList.add(oz.CSS.shadowCaret),e.insertAdjacentElement("beforeend",o)}restoreCaret(e){let o=e.querySelector(`.${oz.CSS.shadowCaret}`);if(!o)return;new J().expandToTag(o);let i=document.createRange();i.selectNode(o),i.extractContents()}insertContentAtCaretPosition(e){let o=document.createDocumentFragment(),i=document.createElement("div"),n=J.get(),r=J.range;i.innerHTML=e,Array.from(i.childNodes).forEach(e=>o.appendChild(e)),0===o.childNodes.length&&o.appendChild(new Text);let s=o.lastChild;r.deleteContents(),r.insertNode(o);let l=document.createRange(),a=s.nodeType===Node.TEXT_NODE?s:s.firstChild;null!==a&&null!==a.textContent&&l.setStart(a,a.textContent.length),n.removeAllRanges(),n.addRange(l)}}let oU=class e extends Q{constructor(){super(...arguments),this.MIME_TYPE="application/x-editor-js",this.toolsTags={},this.tagsByTool={},this.toolsPatterns=[],this.toolsFiles={},this.exceptionList=[],this.processTool=e=>{try{let o=e.create({},{},!1);if(!1===e.pasteConfig)return void this.exceptionList.push(e.name);if(!T(o.onPaste))return;this.getTagsConfig(e),this.getFilesConfig(e),this.getPatternsConfig(e)}catch(o){E(`Paste handling for \xab${e.name}\xbb Tool hasn't been set up because of the error`,"warn",o)}},this.handlePasteEvent=async e=>{let{BlockManager:o,Toolbar:i}=this.Editor,n=o.setCurrentBlockByChildNode(e.target);!n||this.isNativeBehaviour(e.target)&&!e.clipboardData.types.includes("Files")||n&&this.exceptionList.includes(n.name)||(e.preventDefault(),this.processDataTransfer(e.clipboardData),i.close())}}async prepare(){this.processTools()}toggleReadOnly(e){e?this.unsetCallback():this.setCallback()}async processDataTransfer(e,o=!1){let{Tools:i}=this.Editor,n=e.types;if((n.includes?n.includes("Files"):n.contains("Files"))&&!M(this.toolsFiles))return void await this.processFiles(e.files);let r=e.getData(this.MIME_TYPE),s=e.getData("text/plain"),l=e.getData("text/html");if(r)try{this.insertEditorJSData(JSON.parse(r));return}catch{}o&&s.trim()&&l.trim()&&(l="<p>"+(l.trim()?l:s)+"</p>");let a=ex(l,Object.assign({},Object.keys(this.toolsTags).reduce((e,o)=>(e[o.toLowerCase()]=this.toolsTags[o].sanitizationConfig??{},e),{}),i.getAllInlineToolsSanitizeConfig(),{br:{}}));a.trim()&&a.trim()!==s&&Y.isHTMLString(a)?await this.processText(a,!0):await this.processText(s)}async processText(e,o=!1){let{Caret:i,BlockManager:n}=this.Editor,r=o?this.processHTML(e):this.processPlain(e);if(!r.length)return;if(1===r.length)return void(r[0].isBlock?this.processSingleBlock(r.pop()):this.processInlinePaste(r.pop()));let s=n.currentBlock&&n.currentBlock.tool.isDefault&&n.currentBlock.isEmpty;r.map(async(e,o)=>this.insertBlock(e,0===o&&s)),n.currentBlock&&i.setToBlock(n.currentBlock,i.positions.END)}setCallback(){this.listeners.on(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}unsetCallback(){this.listeners.off(this.Editor.UI.nodes.holder,"paste",this.handlePasteEvent)}processTools(){Array.from(this.Editor.Tools.blockTools.values()).forEach(this.processTool)}collectTagNames(e){return I(e)?[e]:S(e)?Object.keys(e):[]}getTagsConfig(e){if(!1===e.pasteConfig)return;let o=e.pasteConfig.tags||[],i=[];o.forEach(o=>{let n=this.collectTagNames(o);i.push(...n),n.forEach(i=>{if(Object.prototype.hasOwnProperty.call(this.toolsTags,i))return void E(`Paste handler for \xab${e.name}\xbb Tool on \xab${i}\xbb tag is skipped because it is already used by \xab${this.toolsTags[i].tool.name}\xbb Tool.`,"warn");let n=S(o)?o[i]:null;this.toolsTags[i.toUpperCase()]={tool:e,sanitizationConfig:n}})}),this.tagsByTool[e.name]=i.map(e=>e.toUpperCase())}getFilesConfig(e){if(!1===e.pasteConfig)return;let{files:o={}}=e.pasteConfig,{extensions:i,mimeTypes:n}=o;(i||n)&&(i&&!Array.isArray(i)&&(E(`\xabextensions\xbb property of the onDrop config for \xab${e.name}\xbb Tool should be an array`),i=[]),n&&!Array.isArray(n)&&(E(`\xabmimeTypes\xbb property of the onDrop config for \xab${e.name}\xbb Tool should be an array`),n=[]),n&&(n=n.filter(o=>!!/^[-\w]+\/([-+\w]+|\*)$/.test(o)||(E(`MIME type value \xab${o}\xbb for the \xab${e.name}\xbb Tool is not a valid MIME type`,"warn"),!1))),this.toolsFiles[e.name]={extensions:i||[],mimeTypes:n||[]})}getPatternsConfig(e){!1===e.pasteConfig||!e.pasteConfig.patterns||M(e.pasteConfig.patterns)||Object.entries(e.pasteConfig.patterns).forEach(([o,i])=>{i instanceof RegExp||E(`Pattern ${i} for \xab${e.name}\xbb Tool is skipped because it should be a Regexp instance.`,"warn"),this.toolsPatterns.push({key:o,pattern:i,tool:e})})}isNativeBehaviour(e){return Y.isNativeInput(e)}async processFiles(e){let o,{BlockManager:i}=this.Editor;o=(o=await Promise.all(Array.from(e).map(e=>this.processFile(e)))).filter(e=>!!e);let n=i.currentBlock.tool.isDefault&&i.currentBlock.isEmpty;o.forEach((e,o)=>{i.paste(e.type,e.event,0===o&&n)})}async processFile(e){let o=e.name.split(".").pop(),i=Object.entries(this.toolsFiles).find(([i,{mimeTypes:n,extensions:r}])=>{let[s,l]=e.type.split("/"),a=r.find(e=>e.toLowerCase()===o.toLowerCase()),c=n.find(e=>{let[o,i]=e.split("/");return o===s&&(i===l||"*"===i)});return!!a||!!c});if(!i)return;let[n]=i;return{event:this.composePasteEvent("file",{file:e}),type:n}}processHTML(e){let{Tools:o}=this.Editor,i=Y.make("DIV");return i.innerHTML=e,this.getNodes(i).map(e=>{let i,n=o.defaultTool,r=!1;switch(e.nodeType){case Node.DOCUMENT_FRAGMENT_NODE:(i=Y.make("div")).appendChild(e);break;case Node.ELEMENT_NODE:i=e,r=!0,this.toolsTags[i.tagName]&&(n=this.toolsTags[i.tagName].tool)}let{tags:s}=n.pasteConfig||{tags:[]},l=Object.assign({},s.reduce((e,o)=>(this.collectTagNames(o).forEach(i=>{let n=S(o)?o[i]:null;e[i.toLowerCase()]=n||{}}),e),{}),n.baseSanitizeConfig);if("table"===i.tagName.toLowerCase()){let e=ex(i.outerHTML,l);i=Y.make("div",void 0,{innerHTML:e}).firstChild}else i.innerHTML=ex(i.innerHTML,l);let a=this.composePasteEvent("tag",{data:i});return{content:i,isBlock:r,tool:n.name,event:a}}).filter(e=>{let o=Y.isEmpty(e.content),i=Y.isSingleTag(e.content);return!o||i})}processPlain(e){let{defaultBlock:o}=this.config;return e?e.split(/\r?\n/).filter(e=>e.trim()).map(e=>{let i=Y.make("div");i.textContent=e;let n=this.composePasteEvent("tag",{data:i});return{content:i,tool:o,isBlock:!1,event:n}}):[]}async processSingleBlock(e){let{Caret:o,BlockManager:i}=this.Editor,{currentBlock:n}=i;if(!n||e.tool!==n.name||!Y.containsOnlyInlineElements(e.content.innerHTML))return void this.insertBlock(e,(null==n?void 0:n.tool.isDefault)&&n.isEmpty);o.insertContentAtCaretPosition(e.content.innerHTML)}async processInlinePaste(o){let{BlockManager:i,Caret:n}=this.Editor,{content:r}=o;if(i.currentBlock&&i.currentBlock.tool.isDefault&&r.textContent.length<e.PATTERN_PROCESSING_MAX_LENGTH){let e=await this.processPattern(r.textContent);if(e){let o=i.currentBlock&&i.currentBlock.tool.isDefault&&i.currentBlock.isEmpty,r=i.paste(e.tool,e.event,o);n.setToBlock(r,n.positions.END);return}}if(i.currentBlock&&i.currentBlock.currentInput){let e=i.currentBlock.tool.baseSanitizeConfig;document.execCommand("insertHTML",!1,ex(r.innerHTML,e))}else this.insertBlock(o)}async processPattern(e){let o=this.toolsPatterns.find(o=>{let i=o.pattern.exec(e);return!!i&&e===i.shift()});return o?{event:this.composePasteEvent("pattern",{key:o.key,data:e}),tool:o.tool.name}:void 0}insertBlock(e,o=!1){let i,{BlockManager:n,Caret:r}=this.Editor,{currentBlock:s}=n;if(o&&s&&s.isEmpty){i=n.paste(e.tool,e.event,!0),r.setToBlock(i,r.positions.END);return}i=n.paste(e.tool,e.event),r.setToBlock(i,r.positions.END)}insertEditorJSData(e){let{BlockManager:o,Caret:i,Tools:n}=this.Editor;ey(e,e=>n.blockTools.get(e).sanitizeConfig).forEach(({tool:e,data:n},r)=>{let s=!1;0===r&&(s=o.currentBlock&&o.currentBlock.tool.isDefault&&o.currentBlock.isEmpty);let l=o.insert({tool:e,data:n,replace:s});i.setToBlock(l,i.positions.END)})}processElementNode(e,o,i){let n=Object.keys(this.toolsTags),{tool:r}=this.toolsTags[e.tagName]||{},s=this.tagsByTool[null==r?void 0:r.name]||[],l=n.includes(e.tagName),a=Y.blockElements.includes(e.tagName.toLowerCase()),c=Array.from(e.children).some(({tagName:e})=>n.includes(e)&&!s.includes(e)),d=Array.from(e.children).some(({tagName:e})=>Y.blockElements.includes(e.toLowerCase()));return a||l||c?(!l||c)&&(!a||d||c)?void 0:[...o,i,e]:(i.appendChild(e),[...o,i])}getNodes(e){let o,i=Array.from(e.childNodes),n=(e,i)=>{if(Y.isEmpty(i)&&!Y.isSingleTag(i))return e;let r=e[e.length-1],s=new DocumentFragment;switch(r&&Y.isFragment(r)&&(s=e.pop()),i.nodeType){case Node.ELEMENT_NODE:if(o=this.processElementNode(i,e,s))return o;break;case Node.TEXT_NODE:return s.appendChild(i),[...e,s];default:return[...e,s]}return[...e,...Array.from(i.childNodes).reduce(n,[])]};return i.reduce(n,[])}composePasteEvent(e,o){return new CustomEvent(e,{detail:o})}};oU.PATTERN_PROCESSING_MAX_LENGTH=450;class o$ extends Q{constructor(){super(...arguments),this.isRectSelectionActivated=!1,this.SCROLL_SPEED=3,this.HEIGHT_OF_SCROLL_ZONE=40,this.BOTTOM_SCROLL_ZONE=1,this.TOP_SCROLL_ZONE=2,this.MAIN_MOUSE_BUTTON=0,this.mousedown=!1,this.isScrolling=!1,this.inScrollZone=null,this.startX=0,this.startY=0,this.mouseX=0,this.mouseY=0,this.stackOfSelected=[],this.listenerIds=[]}static get CSS(){return{overlay:"codex-editor-overlay",overlayContainer:"codex-editor-overlay__container",rect:"codex-editor-overlay__rectangle",topScrollZone:"codex-editor-overlay__scroll-zone--top",bottomScrollZone:"codex-editor-overlay__scroll-zone--bottom"}}prepare(){this.enableModuleBindings()}startSelection(e,o){let i=document.elementFromPoint(e-window.pageXOffset,o-window.pageYOffset);i.closest(`.${this.Editor.Toolbar.CSS.toolbar}`)||(this.Editor.BlockSelection.allBlocksSelected=!1,this.clearSelection(),this.stackOfSelected=[]);let n=[`.${eu.CSS.content}`,`.${this.Editor.Toolbar.CSS.toolbar}`,`.${this.Editor.InlineToolbar.CSS.inlineToolbar}`],r=i.closest("."+this.Editor.UI.CSS.editorWrapper),s=n.some(e=>!!i.closest(e));!r||s||(this.mousedown=!0,this.startX=e,this.startY=o)}endSelection(){this.mousedown=!1,this.startX=0,this.startY=0,this.overlayRectangle.style.display="none"}isRectActivated(){return this.isRectSelectionActivated}clearSelection(){this.isRectSelectionActivated=!1}enableModuleBindings(){let{container:e}=this.genHTML();this.listeners.on(e,"mousedown",e=>{this.processMouseDown(e)},!1),this.listeners.on(document.body,"mousemove",R(e=>{this.processMouseMove(e)},10),{passive:!0}),this.listeners.on(document.body,"mouseleave",()=>{this.processMouseLeave()}),this.listeners.on(window,"scroll",R(e=>{this.processScroll(e)},10),{passive:!0}),this.listeners.on(document.body,"mouseup",()=>{this.processMouseUp()},!1)}processMouseDown(e){e.button===this.MAIN_MOUSE_BUTTON&&(null!==e.target.closest(Y.allInputsSelector)||this.startSelection(e.pageX,e.pageY))}processMouseMove(e){this.changingRectangle(e),this.scrollByZones(e.clientY)}processMouseLeave(){this.clearSelection(),this.endSelection()}processScroll(e){this.changingRectangle(e)}processMouseUp(){this.clearSelection(),this.endSelection()}scrollByZones(e){if(this.inScrollZone=null,e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.TOP_SCROLL_ZONE),document.documentElement.clientHeight-e<=this.HEIGHT_OF_SCROLL_ZONE&&(this.inScrollZone=this.BOTTOM_SCROLL_ZONE),!this.inScrollZone){this.isScrolling=!1;return}this.isScrolling||(this.scrollVertical(this.inScrollZone===this.TOP_SCROLL_ZONE?-this.SCROLL_SPEED:this.SCROLL_SPEED),this.isScrolling=!0)}genHTML(){let{UI:e}=this.Editor,o=e.nodes.holder.querySelector("."+e.CSS.editorWrapper),i=Y.make("div",o$.CSS.overlay,{}),n=Y.make("div",o$.CSS.overlayContainer,{}),r=Y.make("div",o$.CSS.rect,{});return n.appendChild(r),i.appendChild(n),o.appendChild(i),this.overlayRectangle=r,{container:o,overlay:i}}scrollVertical(e){if(!(this.inScrollZone&&this.mousedown))return;let o=window.pageYOffset;window.scrollBy(0,e),this.mouseY+=window.pageYOffset-o,setTimeout(()=>{this.scrollVertical(e)},0)}changingRectangle(e){if(!this.mousedown)return;void 0!==e.pageY&&(this.mouseX=e.pageX,this.mouseY=e.pageY);let{rightPos:o,leftPos:i,index:n}=this.genInfoForMouseSelection(),r=this.startX>o&&this.mouseX>o,s=this.startX<i&&this.mouseX<i;this.rectCrossesBlocks=!(r||s),this.isRectSelectionActivated||(this.rectCrossesBlocks=!1,this.isRectSelectionActivated=!0,this.shrinkRectangleToPoint(),this.overlayRectangle.style.display="block"),this.updateRectangleSize(),this.Editor.Toolbar.close(),void 0!==n&&(this.trySelectNextBlock(n),this.inverseSelection(),J.get().removeAllRanges())}shrinkRectangleToPoint(){this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`}inverseSelection(){let e=this.Editor.BlockManager.getBlockByIndex(this.stackOfSelected[0]).selected;if(this.rectCrossesBlocks&&!e)for(let e of this.stackOfSelected)this.Editor.BlockSelection.selectBlockByIndex(e);if(!this.rectCrossesBlocks&&e)for(let e of this.stackOfSelected)this.Editor.BlockSelection.unSelectBlockByIndex(e)}updateRectangleSize(){this.mouseY>=this.startY?(this.overlayRectangle.style.top=`${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.bottom=`calc(100% - ${this.mouseY-window.pageYOffset}px`):(this.overlayRectangle.style.bottom=`calc(100% - ${this.startY-window.pageYOffset}px`,this.overlayRectangle.style.top=`${this.mouseY-window.pageYOffset}px`),this.mouseX>=this.startX?(this.overlayRectangle.style.left=`${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.right=`calc(100% - ${this.mouseX-window.pageXOffset}px`):(this.overlayRectangle.style.right=`calc(100% - ${this.startX-window.pageXOffset}px`,this.overlayRectangle.style.left=`${this.mouseX-window.pageXOffset}px`)}genInfoForMouseSelection(){let e,o=document.body.offsetWidth/2,i=this.mouseY-window.pageYOffset,n=document.elementFromPoint(o,i),r=this.Editor.BlockManager.getBlockByChildNode(n);void 0!==r&&(e=this.Editor.BlockManager.blocks.findIndex(e=>e.holder===r.holder));let s=this.Editor.BlockManager.lastBlock.holder.querySelector("."+eu.CSS.content),l=Number.parseInt(window.getComputedStyle(s).width,10)/2;return{index:e,leftPos:o-l,rightPos:o+l}}addBlockInSelection(e){this.rectCrossesBlocks&&this.Editor.BlockSelection.selectBlockByIndex(e),this.stackOfSelected.push(e)}trySelectNextBlock(e){let o=this.stackOfSelected[this.stackOfSelected.length-1]===e,i=this.stackOfSelected.length;if(o)return;let n=this.stackOfSelected[i-1]-this.stackOfSelected[i-2]>0,r=0;i>1&&(r=n?1:-1);let s=e>this.stackOfSelected[i-1]&&1===r,l=e<this.stackOfSelected[i-1]&&-1===r,a=!(s||l||0===r);if(!a&&(e>this.stackOfSelected[i-1]||void 0===this.stackOfSelected[i-1])){let o=this.stackOfSelected[i-1]+1||e;for(;o<=e;o++)this.addBlockInSelection(o);return}if(!a&&e<this.stackOfSelected[i-1]){for(let o=this.stackOfSelected[i-1]-1;o>=e;o--)this.addBlockInSelection(o);return}if(!a)return;let c=i-1,d;for(d=e>this.stackOfSelected[i-1]?()=>e>this.stackOfSelected[c]:()=>e<this.stackOfSelected[c];d();)this.rectCrossesBlocks&&this.Editor.BlockSelection.unSelectBlockByIndex(this.stackOfSelected[c]),this.stackOfSelected.pop(),c--}}try{if("u">typeof document){var oY=document.createElement("style");oY.appendChild(document.createTextNode(".ce-paragraph{line-height:1.6em;outline:none}.ce-block:only-of-type .ce-paragraph[data-placeholder-active]:empty:before,.ce-block:only-of-type .ce-paragraph[data-placeholder-active][data-empty=true]:before{content:attr(data-placeholder-active)}.ce-paragraph p:first-of-type{margin-top:0}.ce-paragraph p:last-of-type{margin-bottom:0}")),document.head.appendChild(oY)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}class oW{static get DEFAULT_PLACEHOLDER(){return""}constructor({data:e,config:o,api:i,readOnly:n}){this.api=i,this.readOnly=n,this._CSS={block:this.api.styles.block,wrapper:"ce-paragraph"},this.readOnly||(this.onKeyUp=this.onKeyUp.bind(this)),this._placeholder=o.placeholder?o.placeholder:oW.DEFAULT_PLACEHOLDER,this._data=e??{},this._element=null,this._preserveBlank=o.preserveBlank??!1}onKeyUp(e){if("Backspace"!==e.code&&"Delete"!==e.code||!this._element)return;let{textContent:o}=this._element;""===o&&(this._element.innerHTML="")}drawView(){let e=document.createElement("DIV");return e.classList.add(this._CSS.wrapper,this._CSS.block),e.contentEditable="false",e.dataset.placeholderActive=this.api.i18n.t(this._placeholder),this._data.text&&(e.innerHTML=this._data.text),this.readOnly||(e.contentEditable="true",e.addEventListener("keyup",this.onKeyUp)),e}render(){return this._element=this.drawView(),this._element}merge(e){if(!this._element)return;this._data.text+=e.text;let o=function(e){let o=document.createElement("div");o.innerHTML=e.trim();let i=document.createDocumentFragment();return i.append(...Array.from(o.childNodes)),i}(e.text);this._element.appendChild(o),this._element.normalize()}validate(e){return!(""===e.text.trim()&&!this._preserveBlank)}save(e){return{text:e.innerHTML}}onPaste(e){let o={text:e.detail.data.innerHTML};this._data=o,window.requestAnimationFrame(()=>{this._element&&(this._element.innerHTML=this._data.text||"")})}static get conversionConfig(){return{export:"text",import:"text"}}static get sanitize(){return{text:{br:!0}}}static get isReadOnlySupported(){return!0}static get pasteConfig(){return{tags:["P"]}}static get toolbox(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14"/></svg>',title:"Text"}}}class oK{constructor(){this.commandName="bold"}static get sanitize(){return{b:{}}}render(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 12L9 7.1C9 7.04477 9.04477 7 9.1 7H10.4C11.5 7 14 7.1 14 9.5C14 9.5 14 12 11 12M9 12V16.8C9 16.9105 9.08954 17 9.2 17H12.5C14 17 15 16 15 14.5C15 11.7046 11 12 11 12M9 12H11"/></svg>',name:"bold",onActivate:()=>{document.execCommand(this.commandName)},isActive:()=>document.queryCommandState(this.commandName)}}get shortcut(){return"CMD+B"}}oK.isInline=!0,oK.title="Bold";class oX{constructor(){this.commandName="italic",this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--italic"},this.nodes={button:null}}static get sanitize(){return{i:{}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M13.34 10C12.4223 12.7337 11 17 11 17"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M14.21 7H14.2"/></svg>',this.nodes.button}surround(){document.execCommand(this.commandName)}checkState(){let e=document.queryCommandState(this.commandName);return this.nodes.button.classList.toggle(this.CSS.buttonActive,e),e}get shortcut(){return"CMD+I"}}oX.isInline=!0,oX.title="Italic";class oV{constructor({api:e}){this.commandLink="createLink",this.commandUnlink="unlink",this.ENTER_KEY=13,this.CSS={button:"ce-inline-tool",buttonActive:"ce-inline-tool--active",buttonModifier:"ce-inline-tool--link",buttonUnlink:"ce-inline-tool--unlink",input:"ce-inline-tool-input",inputShowed:"ce-inline-tool-input--showed"},this.nodes={button:null,input:null},this.inputOpened=!1,this.toolbar=e.toolbar,this.inlineToolbar=e.inlineToolbar,this.notifier=e.notifier,this.i18n=e.i18n,this.selection=new J}static get sanitize(){return{a:{href:!0,target:"_blank",rel:"nofollow"}}}render(){return this.nodes.button=document.createElement("button"),this.nodes.button.type="button",this.nodes.button.classList.add(this.CSS.button,this.CSS.buttonModifier),this.nodes.button.innerHTML=eM,this.nodes.button}renderActions(){return this.nodes.input=document.createElement("input"),this.nodes.input.placeholder=this.i18n.t("Add a link"),this.nodes.input.enterKeyHint="done",this.nodes.input.classList.add(this.CSS.input),this.nodes.input.addEventListener("keydown",e=>{e.keyCode===this.ENTER_KEY&&this.enterPressed(e)}),this.nodes.input}surround(e){if(e){this.inputOpened?(this.selection.restore(),this.selection.removeFakeBackground()):(this.selection.setFakeBackground(),this.selection.save());let e=this.selection.findParentTag("A");if(e){this.selection.expandToTag(e),this.unlink(),this.closeActions(),this.checkState(),this.toolbar.close();return}}this.toggleActions()}checkState(){let e=this.selection.findParentTag("A");if(e){this.nodes.button.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M15.7795 11.5C15.7795 11.5 16.053 11.1962 16.5497 10.6722C17.4442 9.72856 17.4701 8.2475 16.5781 7.30145V7.30145C15.6482 6.31522 14.0873 6.29227 13.1288 7.25073L11.8796 8.49999"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8.24517 12.3883C8.24517 12.3883 7.97171 12.6922 7.47504 13.2161C6.58051 14.1598 6.55467 15.6408 7.44666 16.5869V16.5869C8.37653 17.5731 9.93744 17.5961 10.8959 16.6376L12.1452 15.3883"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M17.7802 15.1032L16.597 14.9422C16.0109 14.8624 15.4841 15.3059 15.4627 15.8969L15.4199 17.0818"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M6.39064 9.03238L7.58432 9.06668C8.17551 9.08366 8.6522 8.58665 8.61056 7.99669L8.5271 6.81397"/><line x1="12.1142" x2="11.7" y1="12.2" y2="11.7858" stroke="currentColor" stroke-linecap="round" stroke-width="2"/></svg>',this.nodes.button.classList.add(this.CSS.buttonUnlink),this.nodes.button.classList.add(this.CSS.buttonActive),this.openActions();let o=e.getAttribute("href");this.nodes.input.value="null"!==o?o:"",this.selection.save()}else this.nodes.button.innerHTML=eM,this.nodes.button.classList.remove(this.CSS.buttonUnlink),this.nodes.button.classList.remove(this.CSS.buttonActive);return!!e}clear(){this.closeActions()}get shortcut(){return"CMD+K"}toggleActions(){this.inputOpened?this.closeActions(!1):this.openActions(!0)}openActions(e=!1){this.nodes.input.classList.add(this.CSS.inputShowed),e&&this.nodes.input.focus(),this.inputOpened=!0}closeActions(e=!0){if(this.selection.isFakeBackgroundEnabled){let e=new J;e.save(),this.selection.restore(),this.selection.removeFakeBackground(),e.restore()}this.nodes.input.classList.remove(this.CSS.inputShowed),this.nodes.input.value="",e&&this.selection.clearSaved(),this.inputOpened=!1}enterPressed(e){let o=this.nodes.input.value||"";if(!o.trim()){this.selection.restore(),this.unlink(),e.preventDefault(),this.closeActions();return}if(!this.validateURL(o)){this.notifier.show({message:"Pasted link is not valid.",style:"error"}),E("Incorrect Link pasted","warn",o);return}o=this.prepareLink(o),this.selection.restore(),this.selection.removeFakeBackground(),this.insertLink(o),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.selection.collapseToEnd(),this.inlineToolbar.close()}validateURL(e){return!/\s/.test(e)}prepareLink(e){return e=e.trim(),e=this.addProtocol(e)}addProtocol(e){if(/^(\w+):(\/\/)?/.test(e))return e;let o=/^\/[^/\s]/.test(e),i="#"===e.substring(0,1),n=/^\/\/[^/\s]/.test(e);return o||i||n||(e="http://"+e),e}insertLink(e){let o=this.selection.findParentTag("A");o&&this.selection.expandToTag(o),document.execCommand(this.commandLink,!1,e)}unlink(){document.execCommand(this.commandUnlink)}}oV.isInline=!0,oV.title="Link";class oZ{constructor({api:e}){this.i18nAPI=e.i18n,this.blocksAPI=e.blocks,this.selectionAPI=e.selection,this.toolsAPI=e.tools,this.caretAPI=e.caret}async render(){let e=J.get(),o=this.blocksAPI.getBlockByElement(e.anchorNode);if(void 0===o)return[];let i=this.toolsAPI.getBlockTools(),n=await ea(o,i);if(0===n.length)return[];let r=n.reduce((e,i)=>{var n;return null==(n=i.toolbox)||n.forEach(n=>{e.push({icon:n.icon,title:X.t(eI.toolNames,n.title),name:i.name,closeOnActivate:!0,onActivate:async()=>{let e=await this.blocksAPI.convert(o.id,i.name,n.data);this.caretAPI.setToBlock(e,"end")}})}),e},[]),s=await o.getActiveToolboxEntry(),l=void 0!==s?s.icon:eA,a=!U();return{icon:l,name:"convert-to",hint:{title:this.i18nAPI.t("Convert to")},children:{searchable:a,items:r,onOpen:()=>{a&&(this.selectionAPI.setFakeBackground(),this.selectionAPI.save())},onClose:()=>{a&&(this.selectionAPI.restore(),this.selectionAPI.removeFakeBackground())}}}}}oZ.isInline=!0;class oq{constructor({data:e,api:o}){this.CSS={wrapper:"ce-stub",info:"ce-stub__info",title:"ce-stub__title",subtitle:"ce-stub__subtitle"},this.api=o,this.title=e.title||this.api.i18n.t("Error"),this.subtitle=this.api.i18n.t("The block can not be displayed correctly."),this.savedData=e.savedData,this.wrapper=this.make()}render(){return this.wrapper}save(){return this.savedData}make(){let e=Y.make("div",this.CSS.wrapper),o=Y.make("div",this.CSS.info),i=Y.make("div",this.CSS.title,{textContent:this.title}),n=Y.make("div",this.CSS.subtitle,{textContent:this.subtitle});return e.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><rect width="14" height="14" x="5" y="5" stroke="currentColor" stroke-width="2" rx="4"/><line x1="12" x2="12" y1="9" y2="12" stroke="currentColor" stroke-linecap="round" stroke-width="2"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 15.02V15.01"/></svg>',o.appendChild(i),o.appendChild(n),e.appendChild(o),e}}oq.isReadOnlySupported=!0;class oG extends tk{constructor(){super(...arguments),this.type=tu.Inline}get title(){return this.constructable[tb.Title]}create(){return new this.constructable({api:this.api,config:this.settings})}get isReadOnlySupported(){return this.constructable[tb.IsReadOnlySupported]??!1}}class oQ extends tk{constructor(){super(...arguments),this.type=tu.Tune}create(e,o){return new this.constructable({api:this.api,config:this.settings,block:o,data:e})}}class oJ extends Map{get blockTools(){return new oJ(Array.from(this.entries()).filter(([,e])=>e.isBlock()))}get inlineTools(){return new oJ(Array.from(this.entries()).filter(([,e])=>e.isInline()))}get blockTunes(){return new oJ(Array.from(this.entries()).filter(([,e])=>e.isTune()))}get internalTools(){return new oJ(Array.from(this.entries()).filter(([,e])=>e.isInternal))}get externalTools(){return new oJ(Array.from(this.entries()).filter(([,e])=>!e.isInternal))}}var o0=Object.defineProperty,o1=Object.getOwnPropertyDescriptor,o2=(e,o,i,n)=>{for(var r,s=n>1?void 0:n?o1(o,i):o,l=e.length-1;l>=0;l--)(r=e[l])&&(s=(n?r(o,i,s):r(s))||s);return n&&s&&o0(o,i,s),s};class o5 extends tk{constructor(){super(...arguments),this.type=tu.Block,this.inlineTools=new oJ,this.tunes=new oJ}create(e,o,i){return new this.constructable({data:e,block:o,readOnly:i,api:this.api,config:this.settings})}get isReadOnlySupported(){return!0===this.constructable[tm.IsReadOnlySupported]}get isLineBreaksEnabled(){return this.constructable[tm.IsEnabledLineBreaks]}get toolbox(){let e=this.constructable[tm.Toolbox],o=this.config[tf.Toolbox];if(!M(e)&&!1!==o)return o?Array.isArray(e)?Array.isArray(o)?o.map((o,i)=>{let n=e[i];return n?{...n,...o}:o}):[o]:Array.isArray(o)?o:[{...e,...o}]:Array.isArray(e)?e:[e]}get conversionConfig(){return this.constructable[tm.ConversionConfig]}get enabledInlineTools(){return this.config[tf.EnabledInlineTools]||!1}get enabledBlockTunes(){return this.config[tf.EnabledBlockTunes]}get pasteConfig(){return this.constructable[tm.PasteConfig]??{}}get sanitizeConfig(){let e=super.sanitizeConfig,o=this.baseSanitizeConfig;if(M(e))return o;let i={};for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)){let r=e[n];S(r)?i[n]=Object.assign({},o,r):i[n]=r}return i}get baseSanitizeConfig(){let e={};return Array.from(this.inlineTools.values()).forEach(o=>Object.assign(e,o.sanitizeConfig)),Array.from(this.tunes.values()).forEach(o=>Object.assign(e,o.sanitizeConfig)),e}}o2([z],o5.prototype,"sanitizeConfig",1),o2([z],o5.prototype,"baseSanitizeConfig",1);class o4{constructor(e,o,i){this.api=i,this.config=e,this.editorConfig=o}get(e){let{class:o,isInternal:i=!1,...n}=this.config[e],r=this.getConstructor(o),s=o[tv.IsTune];return new r({name:e,constructable:o,config:n,api:this.api.getMethodsForTool(e,s),isDefault:e===this.editorConfig.defaultBlock,defaultPlaceholder:this.editorConfig.placeholder,isInternal:i})}getConstructor(e){switch(!0){case e[tb.IsInline]:return oG;case e[tv.IsTune]:return oQ;default:return o5}}}class o3{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 10L11.8586 14.8586C11.9367 14.9367 12.0633 14.9367 12.1414 14.8586L17 10"/></svg>',title:this.api.i18n.t("Move down"),onActivate:()=>this.handleClick(),name:"move-down"}}handleClick(){let e=this.api.blocks.getCurrentBlockIndex(),o=this.api.blocks.getBlockByIndex(e+1);if(!o)throw Error("Unable to move Block down since it is already the last");let i=o.holder,n=i.getBoundingClientRect(),r=Math.abs(window.innerHeight-i.offsetHeight);n.top<window.innerHeight&&(r=window.scrollY+i.offsetHeight),window.scrollTo(0,r),this.api.blocks.move(e+1),this.api.toolbar.toggleBlockSettings(!0)}}o3.isTune=!0;class o6{constructor({api:e}){this.api=e}render(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 8L12 12M12 12L16 16M12 12L16 8M12 12L8 16"/></svg>',title:this.api.i18n.t("Delete"),name:"delete",confirmation:{title:this.api.i18n.t("Click to delete"),onActivate:()=>this.handleClick()}}}handleClick(){this.api.blocks.delete()}}o6.isTune=!0;class o8{constructor({api:e}){this.CSS={animation:"wobble"},this.api=e}render(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M7 15L11.8586 10.1414C11.9367 10.0633 12.0633 10.0633 12.1414 10.1414L17 15"/></svg>',title:this.api.i18n.t("Move up"),onActivate:()=>this.handleClick(),name:"move-up"}}handleClick(){let e,o=this.api.blocks.getCurrentBlockIndex(),i=this.api.blocks.getBlockByIndex(o),n=this.api.blocks.getBlockByIndex(o-1);if(0===o||!i||!n)throw Error("Unable to move Block up since it is already the first");let r=i.holder,s=n.holder,l=r.getBoundingClientRect(),a=s.getBoundingClientRect();e=a.top>0?Math.abs(l.top)-Math.abs(a.top):Math.abs(l.top)+a.height,window.scrollBy(0,-1*e),this.api.blocks.move(o-1),this.api.toolbar.toggleBlockSettings(!0)}}o8.isTune=!0;var o7=Object.defineProperty,o9=Object.getOwnPropertyDescriptor;class ie extends Q{constructor(){super(...arguments),this.stubTool="stub",this.toolsAvailable=new oJ,this.toolsUnavailable=new oJ}get available(){return this.toolsAvailable}get unavailable(){return this.toolsUnavailable}get inlineTools(){return this.available.inlineTools}get blockTools(){return this.available.blockTools}get blockTunes(){return this.available.blockTunes}get defaultTool(){return this.blockTools.get(this.config.defaultBlock)}get internal(){return this.available.internalTools}async prepare(){if(this.validateTools(),this.config.tools=function e(o,...i){if(!i.length)return o;let n=i.shift();if(S(o)&&S(n))for(let i in n)S(n[i])?(o[i]||Object.assign(o,{[i]:{}}),e(o[i],n[i])):Object.assign(o,{[i]:n[i]});return e(o,...i)}({},this.internalTools,this.config.tools),!Object.prototype.hasOwnProperty.call(this.config,"tools")||0===Object.keys(this.config.tools).length)throw Error("Can't start without tools");let e=this.prepareConfig();this.factory=new o4(e,this.config,this.Editor.API);let o=this.getListOfPrepareFunctions(e);if(0===o.length)return Promise.resolve();await L(o,e=>{this.toolPrepareMethodSuccess(e)},e=>{this.toolPrepareMethodFallback(e)}),this.prepareBlockTools()}getAllInlineToolsSanitizeConfig(){let e={};return Array.from(this.inlineTools.values()).forEach(o=>{Object.assign(e,o.sanitizeConfig)}),e}destroy(){Object.values(this.available).forEach(async e=>{T(e.reset)&&await e.reset()})}get internalTools(){return{convertTo:{class:oZ,isInternal:!0},link:{class:oV,isInternal:!0},bold:{class:oK,isInternal:!0},italic:{class:oX,isInternal:!0},paragraph:{class:oW,inlineToolbar:!0,isInternal:!0},stub:{class:oq,isInternal:!0},moveUp:{class:o8,isInternal:!0},delete:{class:o6,isInternal:!0},moveDown:{class:o3,isInternal:!0}}}toolPrepareMethodSuccess(e){let o=this.factory.get(e.toolName);if(o.isInline()){let e=["render"].filter(e=>!o.create()[e]);if(e.length){E(`Incorrect Inline Tool: ${o.name}. Some of required methods is not implemented %o`,"warn",e),this.toolsUnavailable.set(o.name,o);return}}this.toolsAvailable.set(o.name,o)}toolPrepareMethodFallback(e){this.toolsUnavailable.set(e.toolName,this.factory.get(e.toolName))}getListOfPrepareFunctions(e){let o=[];return Object.entries(e).forEach(([e,i])=>{o.push({function:T(i.class.prepare)?i.class.prepare:()=>{},data:{toolName:e,config:i.config}})}),o}prepareBlockTools(){Array.from(this.blockTools.values()).forEach(e=>{this.assignInlineToolsToBlockTool(e),this.assignBlockTunesToBlockTool(e)})}assignInlineToolsToBlockTool(e){if(!1!==this.config.inlineToolbar){if(!0===e.enabledInlineTools){e.inlineTools=new oJ(Array.isArray(this.config.inlineToolbar)?this.config.inlineToolbar.map(e=>[e,this.inlineTools.get(e)]):Array.from(this.inlineTools.entries()));return}Array.isArray(e.enabledInlineTools)&&(e.inlineTools=new oJ(["convertTo",...e.enabledInlineTools].map(e=>[e,this.inlineTools.get(e)])))}}assignBlockTunesToBlockTool(e){if(!1!==e.enabledBlockTunes){if(Array.isArray(e.enabledBlockTunes)){let o=new oJ(e.enabledBlockTunes.map(e=>[e,this.blockTunes.get(e)]));e.tunes=new oJ([...o,...this.blockTunes.internalTools]);return}if(Array.isArray(this.config.tunes)){let o=new oJ(this.config.tunes.map(e=>[e,this.blockTunes.get(e)]));e.tunes=new oJ([...o,...this.blockTunes.internalTools]);return}e.tunes=this.blockTunes.internalTools}}validateTools(){for(let e in this.config.tools)if(Object.prototype.hasOwnProperty.call(this.config.tools,e)){if(e in this.internalTools)return;let o=this.config.tools[e];if(!T(o)&&!T(o.class))throw Error(`Tool \xab${e}\xbb must be a constructor function or an object with function in the \xabclass\xbb property`)}}prepareConfig(){let e={};for(let o in this.config.tools)S(this.config.tools[o])?e[o]=this.config.tools[o]:e[o]={class:this.config.tools[o]};return e}}((e,o,i,n)=>{for(var r,s=n>1?void 0:n?o9(o,i):o,l=e.length-1;l>=0;l--)(r=e[l])&&(s=(n?r(o,i,s):r(s))||s);return n&&s&&o7(o,i,s)})([z],ie.prototype,"getAllInlineToolsSanitizeConfig",1);let it=`:root{--selectionColor: #e1f2ff;--inlineSelectionColor: #d4ecff;--bg-light: #eff2f5;--grayText: #707684;--color-dark: #1D202B;--color-active-icon: #388AE5;--color-gray-border: rgba(201, 201, 204, .48);--content-width: 650px;--narrow-mode-right-padding: 50px;--toolbox-buttons-size: 26px;--toolbox-buttons-size--mobile: 36px;--icon-size: 20px;--icon-size--mobile: 28px;--block-padding-vertical: .4em;--color-line-gray: #EFF0F1 }.codex-editor{position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;z-index:1}.codex-editor .hide{display:none}.codex-editor__redactor [contenteditable]:empty:after{content:"\\feff"}@media (min-width: 651px){.codex-editor--narrow .codex-editor__redactor{margin-right:50px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .codex-editor__redactor{margin-left:50px;margin-right:0}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__actions{right:-5px}}.codex-editor-copyable{position:absolute;height:1px;width:1px;top:-400%;opacity:.001}.codex-editor-overlay{position:fixed;top:0;left:0;right:0;bottom:0;z-index:999;pointer-events:none;overflow:hidden}.codex-editor-overlay__container{position:relative;pointer-events:auto;z-index:0}.codex-editor-overlay__rectangle{position:absolute;pointer-events:none;background-color:#2eaadc33;border:1px solid transparent}.codex-editor svg{max-height:100%}.codex-editor path{stroke:currentColor}.codex-editor ::-moz-selection{background-color:#d4ecff}.codex-editor ::selection{background-color:#d4ecff}.codex-editor--toolbox-opened [contentEditable=true][data-placeholder]:focus:before{opacity:0!important}.ce-scroll-locked{overflow:hidden}.ce-scroll-locked--hard{overflow:hidden;top:calc(-1 * var(--window-scroll-offset));position:fixed;width:100%}.ce-toolbar{position:absolute;left:0;right:0;top:0;-webkit-transition:opacity .1s ease;transition:opacity .1s ease;will-change:opacity,top;display:none}.ce-toolbar--opened{display:block}.ce-toolbar__content{max-width:650px;margin:0 auto;position:relative}.ce-toolbar__plus{color:#1d202b;cursor:pointer;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-ms-flex-negative:0;flex-shrink:0}@media (max-width: 650px){.ce-toolbar__plus{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__plus:hover{background-color:#eff2f5}}.ce-toolbar__plus--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}.ce-toolbar__plus-shortcut{opacity:.6;word-spacing:-2px;margin-top:5px}@media (max-width: 650px){.ce-toolbar__plus{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__plus--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__plus--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__actions{position:absolute;right:100%;opacity:0;display:-webkit-box;display:-ms-flexbox;display:flex;padding-right:5px}.ce-toolbar__actions--opened{opacity:1}@media (max-width: 650px){.ce-toolbar__actions{right:auto}}.ce-toolbar__settings-btn{color:#1d202b;width:26px;height:26px;border-radius:7px;display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;margin-left:3px;cursor:pointer;user-select:none}@media (max-width: 650px){.ce-toolbar__settings-btn{width:36px;height:36px}}@media (hover: hover){.ce-toolbar__settings-btn:hover{background-color:#eff2f5}}.ce-toolbar__settings-btn--active{background-color:#eff2f5;-webkit-animation:bounceIn .75s 1;animation:bounceIn .75s 1;-webkit-animation-fill-mode:forwards;animation-fill-mode:forwards}@media (min-width: 651px){.ce-toolbar__settings-btn{width:24px}}.ce-toolbar__settings-btn--hidden{display:none}@media (max-width: 650px){.ce-toolbar__settings-btn{position:absolute;background-color:#fff;border:1px solid #E8E8EB;-webkit-box-shadow:0 3px 15px -3px rgba(13,20,33,.13);box-shadow:0 3px 15px -3px #0d142121;border-radius:6px;z-index:2;position:static}.ce-toolbar__settings-btn--left-oriented:before{left:15px;margin-left:0}.ce-toolbar__settings-btn--right-oriented:before{left:auto;right:15px;margin-left:0}}.ce-toolbar__plus svg,.ce-toolbar__settings-btn svg{width:24px;height:24px}@media (min-width: 651px){.codex-editor--narrow .ce-toolbar__plus{left:5px}}@media (min-width: 651px){.codex-editor--narrow .ce-toolbox .ce-popover{right:0;left:auto;left:initial}}.ce-inline-toolbar{--y-offset: 8px;--color-background-icon-active: rgba(56, 138, 229, .1);--color-text-icon-active: #388AE5;--color-text-primary: black;position:absolute;visibility:hidden;-webkit-transition:opacity .25s ease;transition:opacity .25s ease;will-change:opacity,left,top;top:0;left:0;z-index:3;opacity:1;visibility:visible}.ce-inline-toolbar [hidden]{display:none!important}.ce-inline-toolbar__toggler-and-button-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;width:100%;padding:0 6px}.ce-inline-toolbar__buttons{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown{display:-webkit-box;display:-ms-flexbox;display:flex;padding:6px;margin:0 6px 0 -6px;-webkit-box-align:center;-ms-flex-align:center;align-items:center;cursor:pointer;border-right:1px solid rgba(201,201,204,.48);-webkit-box-sizing:border-box;box-sizing:border-box}@media (hover: hover){.ce-inline-toolbar__dropdown:hover{background:#eff2f5}}.ce-inline-toolbar__dropdown--hidden{display:none}.ce-inline-toolbar__dropdown-content,.ce-inline-toolbar__dropdown-arrow{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-inline-toolbar__dropdown-content svg,.ce-inline-toolbar__dropdown-arrow svg{width:20px;height:20px}.ce-inline-toolbar__shortcut{opacity:.6;word-spacing:-3px;margin-top:3px}.ce-inline-tool{color:var(--color-text-primary);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;border:0;border-radius:4px;line-height:normal;height:100%;padding:0;width:28px;background-color:transparent;cursor:pointer}@media (max-width: 650px){.ce-inline-tool{width:36px;height:36px}}@media (hover: hover){.ce-inline-tool:hover{background-color:#f8f8f8}}.ce-inline-tool svg{display:block;width:20px;height:20px}@media (max-width: 650px){.ce-inline-tool svg{width:28px;height:28px}}.ce-inline-tool--link .icon--unlink,.ce-inline-tool--unlink .icon--link{display:none}.ce-inline-tool--unlink .icon--unlink{display:inline-block;margin-bottom:-1px}.ce-inline-tool-input{background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:4px 8px;font-size:14px;line-height:22px;outline:none;margin:0;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box;display:none;font-weight:500;-webkit-appearance:none;font-family:inherit}@media (max-width: 650px){.ce-inline-tool-input{font-size:15px;font-weight:500}}.ce-inline-tool-input::-webkit-input-placeholder{color:#707684}.ce-inline-tool-input::-moz-placeholder{color:#707684}.ce-inline-tool-input:-ms-input-placeholder{color:#707684}.ce-inline-tool-input::-ms-input-placeholder{color:#707684}.ce-inline-tool-input::placeholder{color:#707684}.ce-inline-tool-input--showed{display:block}.ce-inline-tool--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}@-webkit-keyframes fade-in{0%{opacity:0}to{opacity:1}}@keyframes fade-in{0%{opacity:0}to{opacity:1}}.ce-block{-webkit-animation:fade-in .3s ease;animation:fade-in .3s ease;-webkit-animation-fill-mode:none;animation-fill-mode:none;-webkit-animation-fill-mode:initial;animation-fill-mode:initial}.ce-block:first-of-type{margin-top:0}.ce-block--selected .ce-block__content{background:#e1f2ff}.ce-block--selected .ce-block__content [contenteditable]{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.ce-block--selected .ce-block__content img,.ce-block--selected .ce-block__content .ce-stub{opacity:.55}.ce-block--stretched .ce-block__content{max-width:none}.ce-block__content{position:relative;max-width:650px;margin:0 auto;-webkit-transition:background-color .15s ease;transition:background-color .15s ease}.ce-block--drop-target .ce-block__content:before{content:"";position:absolute;top:100%;left:-20px;margin-top:-1px;height:8px;width:8px;border:solid #388AE5;border-width:1px 1px 0 0;-webkit-transform-origin:right;transform-origin:right;-webkit-transform:rotate(45deg);transform:rotate(45deg)}.ce-block--drop-target .ce-block__content:after{content:"";position:absolute;top:100%;height:1px;width:100%;color:#388ae5;background:repeating-linear-gradient(90deg,#388AE5,#388AE5 1px,#fff 1px,#fff 6px)}.ce-block a{cursor:pointer;-webkit-text-decoration:underline;text-decoration:underline}.ce-block b{font-weight:700}.ce-block i{font-style:italic}@-webkit-keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes bounceIn{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.9,.9,.9);transform:scale3d(.9,.9,.9)}20%{-webkit-transform:scale3d(1.03,1.03,1.03);transform:scale3d(1.03,1.03,1.03)}60%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes selectionBounce{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}50%{-webkit-transform:scale3d(1.01,1.01,1.01);transform:scale3d(1.01,1.01,1.01)}70%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@-webkit-keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}@keyframes buttonClicked{0%,20%,40%,60%,80%,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{-webkit-transform:scale3d(.95,.95,.95);transform:scale3d(.95,.95,.95)}60%{-webkit-transform:scale3d(1.02,1.02,1.02);transform:scale3d(1.02,1.02,1.02)}80%{-webkit-transform:scale3d(1,1,1);transform:scaleZ(1)}}.cdx-block{padding:.4em 0}.cdx-block::-webkit-input-placeholder{line-height:normal!important}.cdx-input{border:1px solid rgba(201,201,204,.48);-webkit-box-shadow:inset 0 1px 2px 0 rgba(35,44,72,.06);box-shadow:inset 0 1px 2px #232c480f;border-radius:3px;padding:10px 12px;outline:none;width:100%;-webkit-box-sizing:border-box;box-sizing:border-box}.cdx-input[data-placeholder]:before{position:static!important}.cdx-input[data-placeholder]:before{display:inline-block;width:0;white-space:nowrap;pointer-events:none}.cdx-settings-button{display:-webkit-inline-box;display:-ms-inline-flexbox;display:inline-flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;border-radius:3px;cursor:pointer;border:0;outline:none;background-color:transparent;vertical-align:bottom;color:inherit;margin:0;min-width:26px;min-height:26px}.cdx-settings-button--focused{background:rgba(34,186,255,.08)!important}.cdx-settings-button--focused{-webkit-box-shadow:inset 0 0 0px 1px rgba(7,161,227,.08);box-shadow:inset 0 0 0 1px #07a1e314}.cdx-settings-button--focused-animated{-webkit-animation-name:buttonClicked;animation-name:buttonClicked;-webkit-animation-duration:.25s;animation-duration:.25s}.cdx-settings-button--active{color:#388ae5}.cdx-settings-button svg{width:auto;height:auto}@media (max-width: 650px){.cdx-settings-button svg{width:28px;height:28px}}@media (max-width: 650px){.cdx-settings-button{width:36px;height:36px;border-radius:8px}}@media (hover: hover){.cdx-settings-button:hover{background-color:#eff2f5}}.cdx-loader{position:relative;border:1px solid rgba(201,201,204,.48)}.cdx-loader:before{content:"";position:absolute;left:50%;top:50%;width:18px;height:18px;margin:-11px 0 0 -11px;border:2px solid rgba(201,201,204,.48);border-left-color:#388ae5;border-radius:50%;-webkit-animation:cdxRotation 1.2s infinite linear;animation:cdxRotation 1.2s infinite linear}@-webkit-keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes cdxRotation{0%{-webkit-transform:rotate(0deg);transform:rotate(0)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}.cdx-button{padding:13px;border-radius:3px;border:1px solid rgba(201,201,204,.48);font-size:14.9px;background:#fff;-webkit-box-shadow:0 2px 2px 0 rgba(18,30,57,.04);box-shadow:0 2px 2px #121e390a;color:#707684;text-align:center;cursor:pointer}@media (hover: hover){.cdx-button:hover{background:#FBFCFE;-webkit-box-shadow:0 1px 3px 0 rgba(18,30,57,.08);box-shadow:0 1px 3px #121e3914}}.cdx-button svg{height:20px;margin-right:.2em;margin-top:-2px}.ce-stub{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:12px 18px;margin:10px 0;border-radius:10px;background:#eff2f5;border:1px solid #EFF0F1;color:#707684;font-size:14px}.ce-stub svg{width:20px;height:20px}.ce-stub__info{margin-left:14px}.ce-stub__title{font-weight:500;text-transform:capitalize}.codex-editor.codex-editor--rtl{direction:rtl}.codex-editor.codex-editor--rtl .cdx-list{padding-left:0;padding-right:40px}.codex-editor.codex-editor--rtl .ce-toolbar__plus{right:-26px;left:auto}.codex-editor.codex-editor--rtl .ce-toolbar__actions{right:auto;left:-26px}@media (max-width: 650px){.codex-editor.codex-editor--rtl .ce-toolbar__actions{margin-left:0;margin-right:auto;padding-right:0;padding-left:10px}}.codex-editor.codex-editor--rtl .ce-settings{left:5px;right:auto}.codex-editor.codex-editor--rtl .ce-settings:before{right:auto;left:25px}.codex-editor.codex-editor--rtl .ce-settings__button:not(:nth-child(3n+3)){margin-left:3px;margin-right:0}.codex-editor.codex-editor--rtl .ce-conversion-tool__icon{margin-right:0;margin-left:10px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown{border-right:0px solid transparent;border-left:1px solid rgba(201,201,204,.48);margin:0 -6px 0 6px}.codex-editor.codex-editor--rtl .ce-inline-toolbar__dropdown .icon--toggler-down{margin-left:0;margin-right:4px}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__plus{left:0;right:5px}}@media (min-width: 651px){.codex-editor--narrow.codex-editor--rtl .ce-toolbar__actions{left:-5px}}.cdx-search-field{--icon-margin-right: 10px;background:#F8F8F8;border:1px solid rgba(226,226,229,.2);border-radius:6px;padding:2px;display:grid;grid-template-columns:auto auto 1fr;grid-template-rows:auto}.cdx-search-field__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center;margin-right:var(--icon-margin-right)}.cdx-search-field__icon svg{width:20px;height:20px;color:#707684}.cdx-search-field__input{font-size:14px;outline:none;font-weight:500;font-family:inherit;border:0;background:transparent;margin:0;padding:0;line-height:22px;min-width:calc(100% - 26px - var(--icon-margin-right))}.cdx-search-field__input::-webkit-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-moz-placeholder{color:#707684;font-weight:500}.cdx-search-field__input:-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::-ms-input-placeholder{color:#707684;font-weight:500}.cdx-search-field__input::placeholder{color:#707684;font-weight:500}.ce-popover{--border-radius: 6px;--width: 200px;--max-height: 270px;--padding: 6px;--offset-from-target: 8px;--color-border: #EFF0F1;--color-shadow: rgba(13, 20, 33, .1);--color-background: white;--color-text-primary: black;--color-text-secondary: #707684;--color-border-icon: rgba(201, 201, 204, .48);--color-border-icon-disabled: #EFF0F1;--color-text-icon-active: #388AE5;--color-background-icon-active: rgba(56, 138, 229, .1);--color-background-item-focus: rgba(34, 186, 255, .08);--color-shadow-item-focus: rgba(7, 161, 227, .08);--color-background-item-hover: #F8F8F8;--color-background-item-confirm: #E24A4A;--color-background-item-confirm-hover: #CE4343;--popover-top: calc(100% + var(--offset-from-target));--popover-left: 0;--nested-popover-overlap: 4px;--icon-size: 20px;--item-padding: 3px;--item-height: calc(var(--icon-size) + 2 * var(--item-padding))}.ce-popover__container{min-width:var(--width);width:var(--width);max-height:var(--max-height);border-radius:var(--border-radius);overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-box-shadow:0px 3px 15px -3px var(--color-shadow);box-shadow:0 3px 15px -3px var(--color-shadow);position:absolute;left:var(--popover-left);top:var(--popover-top);background:var(--color-background);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;z-index:4;opacity:0;max-height:0;pointer-events:none;padding:0;border:none}.ce-popover--opened>.ce-popover__container{opacity:1;padding:var(--padding);max-height:var(--max-height);pointer-events:auto;-webkit-animation:panelShowing .1s ease;animation:panelShowing .1s ease;border:1px solid var(--color-border)}@media (max-width: 650px){.ce-popover--opened>.ce-popover__container{-webkit-animation:panelShowingMobile .25s ease;animation:panelShowingMobile .25s ease}}.ce-popover--open-top .ce-popover__container{--popover-top: calc(-1 * (var(--offset-from-target) + var(--popover-height)))}.ce-popover--open-left .ce-popover__container{--popover-left: calc(-1 * var(--width) + 100%)}.ce-popover__items{overflow-y:auto;-ms-scroll-chaining:none;overscroll-behavior:contain}@media (max-width: 650px){.ce-popover__overlay{position:fixed;top:0;bottom:0;left:0;right:0;background:#1D202B;z-index:3;opacity:.5;-webkit-transition:opacity .12s ease-in;transition:opacity .12s ease-in;will-change:opacity;visibility:visible}}.ce-popover__overlay--hidden{display:none}@media (max-width: 650px){.ce-popover .ce-popover__container{--offset: 5px;position:fixed;max-width:none;min-width:calc(100% - var(--offset) * 2);left:var(--offset);right:var(--offset);bottom:calc(var(--offset) + env(safe-area-inset-bottom));top:auto;border-radius:10px}}.ce-popover__search{margin-bottom:5px}.ce-popover__nothing-found-message{color:#707684;display:none;cursor:default;padding:3px;font-size:14px;line-height:20px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ce-popover__nothing-found-message--displayed{display:block}.ce-popover--nested .ce-popover__container{--popover-left: calc(var(--nesting-level) * (var(--width) - var(--nested-popover-overlap)));top:calc(var(--trigger-item-top) - var(--nested-popover-overlap));position:absolute}.ce-popover--open-top.ce-popover--nested .ce-popover__container{top:calc(var(--trigger-item-top) - var(--popover-height) + var(--item-height) + var(--offset-from-target) + var(--nested-popover-overlap))}.ce-popover--open-left .ce-popover--nested .ce-popover__container{--popover-left: calc(-1 * (var(--nesting-level) + 1) * var(--width) + 100%)}.ce-popover-item-separator{padding:4px 3px}.ce-popover-item-separator--hidden{display:none}.ce-popover-item-separator__line{height:1px;background:var(--color-border);width:100%}.ce-popover-item-html--hidden{display:none}.ce-popover-item{--border-radius: 6px;border-radius:var(--border-radius);display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding:var(--item-padding);color:var(--color-text-primary);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;border:none;background:transparent}@media (max-width: 650px){.ce-popover-item{padding:4px}}.ce-popover-item:not(:last-of-type){margin-bottom:1px}.ce-popover-item__icon{width:26px;height:26px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover-item__icon{width:36px;height:36px;border-radius:8px}.ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover-item__icon--tool{margin-right:4px}.ce-popover-item__title{font-size:14px;line-height:20px;font-weight:500;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;margin-right:auto}@media (max-width: 650px){.ce-popover-item__title{font-size:16px}}.ce-popover-item__secondary-title{color:var(--color-text-secondary);font-size:12px;white-space:nowrap;letter-spacing:-.1em;padding-right:5px;opacity:.6}@media (max-width: 650px){.ce-popover-item__secondary-title{display:none}}.ce-popover-item--active{background:var(--color-background-icon-active);color:var(--color-text-icon-active)}.ce-popover-item--disabled{color:var(--color-text-secondary);cursor:default;pointer-events:none}.ce-popover-item--focused:not(.ce-popover-item--no-focus){background:var(--color-background-item-focus)!important}.ce-popover-item--hidden{display:none}@media (hover: hover){.ce-popover-item:hover{cursor:pointer}.ce-popover-item:hover:not(.ce-popover-item--no-hover){background-color:var(--color-background-item-hover)}}.ce-popover-item--confirmation{background:var(--color-background-item-confirm)}.ce-popover-item--confirmation .ce-popover-item__title,.ce-popover-item--confirmation .ce-popover-item__icon{color:#fff}@media (hover: hover){.ce-popover-item--confirmation:not(.ce-popover-item--no-hover):hover{background:var(--color-background-item-confirm-hover)}}.ce-popover-item--confirmation:not(.ce-popover-item--no-focus).ce-popover-item--focused{background:var(--color-background-item-confirm-hover)!important}@-webkit-keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowing{0%{opacity:0;-webkit-transform:translateY(-8px) scale(.9);transform:translateY(-8px) scale(.9)}70%{opacity:1;-webkit-transform:translateY(2px);transform:translateY(2px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@-webkit-keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes panelShowingMobile{0%{opacity:0;-webkit-transform:translateY(14px) scale(.98);transform:translateY(14px) scale(.98)}70%{opacity:1;-webkit-transform:translateY(-4px);transform:translateY(-4px)}to{-webkit-transform:translateY(0);transform:translateY(0)}}.wobble{-webkit-animation-name:wobble;animation-name:wobble;-webkit-animation-duration:.4s;animation-duration:.4s}@-webkit-keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes wobble{0%{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}15%{-webkit-transform:translate3d(-9%,0,0);transform:translate3d(-9%,0,0)}30%{-webkit-transform:translate3d(9%,0,0);transform:translate3d(9%,0,0)}45%{-webkit-transform:translate3d(-4%,0,0);transform:translate3d(-4%,0,0)}60%{-webkit-transform:translate3d(4%,0,0);transform:translate3d(4%,0,0)}75%{-webkit-transform:translate3d(-1%,0,0);transform:translate3d(-1%,0,0)}to{-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.ce-popover-header{margin-bottom:8px;margin-top:4px;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover-header__text{font-size:18px;font-weight:600}.ce-popover-header__back-button{border:0;background:transparent;width:36px;height:36px;color:var(--color-text-primary)}.ce-popover-header__back-button svg{display:block;width:28px;height:28px}.ce-popover--inline{--height: 38px;--height-mobile: 46px;--container-padding: 4px;position:relative}.ce-popover--inline .ce-popover__custom-content{margin-bottom:0}.ce-popover--inline .ce-popover__items{display:-webkit-box;display:-ms-flexbox;display:flex}.ce-popover--inline .ce-popover__container{-webkit-box-orient:horizontal;-webkit-box-direction:normal;-ms-flex-direction:row;flex-direction:row;padding:var(--container-padding);height:var(--height);top:0;min-width:-webkit-max-content;min-width:-moz-max-content;min-width:max-content;width:-webkit-max-content;width:-moz-max-content;width:max-content;-webkit-animation:none;animation:none}@media (max-width: 650px){.ce-popover--inline .ce-popover__container{height:var(--height-mobile);position:absolute}}.ce-popover--inline .ce-popover-item-separator{padding:0 4px}.ce-popover--inline .ce-popover-item-separator__line{height:100%;width:1px}.ce-popover--inline .ce-popover-item{border-radius:4px;padding:4px}.ce-popover--inline .ce-popover-item__icon--tool{-webkit-box-shadow:none;box-shadow:none;background:transparent;margin-right:0}.ce-popover--inline .ce-popover-item__icon{width:auto;width:initial;height:auto;height:initial}.ce-popover--inline .ce-popover-item__icon svg{width:20px;height:20px}@media (max-width: 650px){.ce-popover--inline .ce-popover-item__icon svg{width:28px;height:28px}}.ce-popover--inline .ce-popover-item:not(:last-of-type){margin-bottom:0;margin-bottom:initial}.ce-popover--inline .ce-popover-item-html{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.ce-popover--inline .ce-popover-item__icon--chevron-right{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{--offset: 3px;left:0;top:calc(var(--height) + var(--offset))}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested-level-1 .ce-popover__container{top:calc(var(--height-mobile) + var(--offset))}}.ce-popover--inline .ce-popover--nested .ce-popover__container{min-width:var(--width);width:var(--width);height:-webkit-fit-content;height:-moz-fit-content;height:fit-content;padding:6px;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.ce-popover--inline .ce-popover--nested .ce-popover__items{display:block;width:100%}.ce-popover--inline .ce-popover--nested .ce-popover-item{border-radius:6px;padding:3px}@media (max-width: 650px){.ce-popover--inline .ce-popover--nested .ce-popover-item{padding:4px}}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon--tool{margin-right:4px}.ce-popover--inline .ce-popover--nested .ce-popover-item__icon{width:26px;height:26px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator{padding:4px 3px}.ce-popover--inline .ce-popover--nested .ce-popover-item-separator__line{width:100%;height:1px}.codex-editor [data-placeholder]:empty:before,.codex-editor [data-placeholder][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text;content:attr(data-placeholder)}.codex-editor [data-placeholder-active]:empty:before,.codex-editor [data-placeholder-active][data-empty=true]:before{pointer-events:none;color:#707684;cursor:text}.codex-editor [data-placeholder-active]:empty:focus:before,.codex-editor [data-placeholder-active][data-empty=true]:focus:before{content:attr(data-placeholder-active)}
`,io={BlocksAPI:class extends Q{constructor(){super(...arguments),this.insert=(e=this.config.defaultBlock,o={},i={},n,r,s,l)=>new q(this.Editor.BlockManager.insert({id:l,tool:e,data:o,index:n,needToFocus:r,replace:s})),this.composeBlockData=async e=>new eu({tool:this.Editor.Tools.blockTools.get(e),api:this.Editor.API,readOnly:!0,data:{},tunesData:{}}).data,this.update=async(e,o,i)=>{let{BlockManager:n}=this.Editor,r=n.getBlockById(e);if(void 0===r)throw Error(`Block with id "${e}" not found`);return new q(await n.update(r,o,i))},this.convert=async(e,o,i)=>{var n,r;let{BlockManager:s,Tools:l}=this.Editor,a=s.getBlockById(e);if(!a)throw Error(`Block with id "${e}" not found`);let c=l.blockTools.get(a.name),d=l.blockTools.get(o);if(!d)throw Error(`Block Tool with type "${o}" not found`);let h=(null==(n=null==c?void 0:c.conversionConfig)?void 0:n.export)!==void 0,p=(null==(r=d.conversionConfig)?void 0:r.import)!==void 0;if(h&&p)return new q(await s.convert(a,o,i));{let e=[!h&&j(a.name),!p&&j(o)].filter(Boolean).join(" and ");throw Error(`Conversion from "${a.name}" to "${o}" is not possible. ${e} tool(s) should provide a "conversionConfig"`)}},this.insertMany=(e,o=this.Editor.BlockManager.blocks.length-1)=>{this.validateIndex(o);let i=e.map(({id:e,type:o,data:i})=>this.Editor.BlockManager.composeBlock({id:e,tool:o||this.config.defaultBlock,data:i}));return this.Editor.BlockManager.insertMany(i,o),i.map(e=>new q(e))}}get methods(){return{clear:()=>this.clear(),render:e=>this.render(e),renderFromHTML:e=>this.renderFromHTML(e),delete:e=>this.delete(e),swap:(e,o)=>this.swap(e,o),move:(e,o)=>this.move(e,o),getBlockByIndex:e=>this.getBlockByIndex(e),getById:e=>this.getById(e),getCurrentBlockIndex:()=>this.getCurrentBlockIndex(),getBlockIndex:e=>this.getBlockIndex(e),getBlocksCount:()=>this.getBlocksCount(),getBlockByElement:e=>this.getBlockByElement(e),stretchBlock:(e,o=!0)=>this.stretchBlock(e,o),insertNewBlock:()=>this.insertNewBlock(),insert:this.insert,insertMany:this.insertMany,update:this.update,composeBlockData:this.composeBlockData,convert:this.convert}}getBlocksCount(){return this.Editor.BlockManager.blocks.length}getCurrentBlockIndex(){return this.Editor.BlockManager.currentBlockIndex}getBlockIndex(e){let o=this.Editor.BlockManager.getBlockById(e);return o?this.Editor.BlockManager.getBlockIndex(o):void C("There is no block with id `"+e+"`","warn")}getBlockByIndex(e){let o=this.Editor.BlockManager.getBlockByIndex(e);return void 0===o?void C("There is no block at index `"+e+"`","warn"):new q(o)}getById(e){let o=this.Editor.BlockManager.getBlockById(e);return void 0===o?(C("There is no block with id `"+e+"`","warn"),null):new q(o)}getBlockByElement(e){let o=this.Editor.BlockManager.getBlock(e);return void 0===o?void C("There is no block corresponding to element `"+e+"`","warn"):new q(o)}swap(e,o){E("`blocks.swap()` method is deprecated and will be removed in the next major release. Use `block.move()` method instead","info"),this.Editor.BlockManager.swap(e,o)}move(e,o){this.Editor.BlockManager.move(e,o)}delete(e=this.Editor.BlockManager.currentBlockIndex){try{let o=this.Editor.BlockManager.getBlockByIndex(e);this.Editor.BlockManager.removeBlock(o)}catch(e){C(e,"warn");return}0===this.Editor.BlockManager.blocks.length&&this.Editor.BlockManager.insert(),this.Editor.BlockManager.currentBlock&&this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END),this.Editor.Toolbar.close()}async clear(){await this.Editor.BlockManager.clear(!0),this.Editor.InlineToolbar.close()}async render(e){if(void 0===e||void 0===e.blocks)throw Error("Incorrect data passed to the render() method");this.Editor.ModificationsObserver.disable(),await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(e.blocks),this.Editor.ModificationsObserver.enable()}renderFromHTML(e){return this.Editor.BlockManager.clear(),this.Editor.Paste.processText(e,!0)}stretchBlock(e,o=!0){H(!0,"blocks.stretchBlock()","BlockAPI");let i=this.Editor.BlockManager.getBlockByIndex(e);i&&(i.stretched=o)}insertNewBlock(){E("Method blocks.insertNewBlock() is deprecated and it will be removed in the next major release. Use blocks.insert() instead.","warn"),this.insert()}validateIndex(e){if("number"!=typeof e)throw Error("Index should be a number");if(e<0||null===e)throw Error("Index should be greater than or equal to 0")}},CaretAPI:class extends Q{constructor(){super(...arguments),this.setToFirstBlock=(e=this.Editor.Caret.positions.DEFAULT,o=0)=>!!this.Editor.BlockManager.firstBlock&&(this.Editor.Caret.setToBlock(this.Editor.BlockManager.firstBlock,e,o),!0),this.setToLastBlock=(e=this.Editor.Caret.positions.DEFAULT,o=0)=>!!this.Editor.BlockManager.lastBlock&&(this.Editor.Caret.setToBlock(this.Editor.BlockManager.lastBlock,e,o),!0),this.setToPreviousBlock=(e=this.Editor.Caret.positions.DEFAULT,o=0)=>!!this.Editor.BlockManager.previousBlock&&(this.Editor.Caret.setToBlock(this.Editor.BlockManager.previousBlock,e,o),!0),this.setToNextBlock=(e=this.Editor.Caret.positions.DEFAULT,o=0)=>!!this.Editor.BlockManager.nextBlock&&(this.Editor.Caret.setToBlock(this.Editor.BlockManager.nextBlock,e,o),!0),this.setToBlock=(e,o=this.Editor.Caret.positions.DEFAULT,i=0)=>{var n;let r=(n=this.Editor,"number"==typeof e?n.BlockManager.getBlockByIndex(e):"string"==typeof e?n.BlockManager.getBlockById(e):n.BlockManager.getBlockById(e.id));return void 0!==r&&(this.Editor.Caret.setToBlock(r,o,i),!0)},this.focus=(e=!1)=>e?this.setToLastBlock(this.Editor.Caret.positions.END):this.setToFirstBlock(this.Editor.Caret.positions.START)}get methods(){return{setToFirstBlock:this.setToFirstBlock,setToLastBlock:this.setToLastBlock,setToPreviousBlock:this.setToPreviousBlock,setToNextBlock:this.setToNextBlock,setToBlock:this.setToBlock,focus:this.focus}}},EventsAPI:class extends Q{get methods(){return{emit:(e,o)=>this.emit(e,o),off:(e,o)=>this.off(e,o),on:(e,o)=>this.on(e,o)}}on(e,o){this.eventsDispatcher.on(e,o)}emit(e,o){this.eventsDispatcher.emit(e,o)}off(e,o){this.eventsDispatcher.off(e,o)}},I18nAPI:ef,API:class extends Q{get methods(){return{blocks:this.Editor.BlocksAPI.methods,caret:this.Editor.CaretAPI.methods,tools:this.Editor.ToolsAPI.methods,events:this.Editor.EventsAPI.methods,listeners:this.Editor.ListenersAPI.methods,notifier:this.Editor.NotifierAPI.methods,sanitizer:this.Editor.SanitizerAPI.methods,saver:this.Editor.SaverAPI.methods,selection:this.Editor.SelectionAPI.methods,styles:this.Editor.StylesAPI.classes,toolbar:this.Editor.ToolbarAPI.methods,inlineToolbar:this.Editor.InlineToolbarAPI.methods,tooltip:this.Editor.TooltipAPI.methods,i18n:this.Editor.I18nAPI.methods,readOnly:this.Editor.ReadOnlyAPI.methods,ui:this.Editor.UiAPI.methods}}getMethodsForTool(e,o){return Object.assign(this.methods,{i18n:this.Editor.I18nAPI.getMethodsForTool(e,o)})}},InlineToolbarAPI:class extends Q{get methods(){return{close:()=>this.close(),open:()=>this.open()}}open(){this.Editor.InlineToolbar.tryToShow()}close(){this.Editor.InlineToolbar.close()}},ListenersAPI:class extends Q{get methods(){return{on:(e,o,i,n)=>this.on(e,o,i,n),off:(e,o,i,n)=>this.off(e,o,i,n),offById:e=>this.offById(e)}}on(e,o,i,n){return this.listeners.on(e,o,i,n)}off(e,o,i,n){this.listeners.off(e,o,i,n)}offById(e){this.listeners.offById(e)}},NotifierAPI:class extends Q{constructor({config:e,eventsDispatcher:o}){super({config:e,eventsDispatcher:o}),this.notifier=new eb}get methods(){return{show:e=>this.show(e)}}show(e){return this.notifier.show(e)}},ReadOnlyAPI:class extends Q{get methods(){return{toggle:e=>this.toggle(e),get isEnabled(){return this.isEnabled}}}toggle(e){return this.Editor.ReadOnly.toggle(e)}get isEnabled(){return this.Editor.ReadOnly.isEnabled}},SanitizerAPI:class extends Q{get methods(){return{clean:(e,o)=>this.clean(e,o)}}clean(e,o){return ex(e,o)}},SaverAPI:class extends Q{get methods(){return{save:()=>this.save()}}save(){let e="Editor's content can not be saved in read-only mode";return this.Editor.ReadOnly.isEnabled?(C(e,"warn"),Promise.reject(Error(e))):this.Editor.Saver.save()}},SelectionAPI:class extends Q{constructor(){super(...arguments),this.selectionUtils=new J}get methods(){return{findParentTag:(e,o)=>this.findParentTag(e,o),expandToTag:e=>this.expandToTag(e),save:()=>this.selectionUtils.save(),restore:()=>this.selectionUtils.restore(),setFakeBackground:()=>this.selectionUtils.setFakeBackground(),removeFakeBackground:()=>this.selectionUtils.removeFakeBackground()}}findParentTag(e,o){return this.selectionUtils.findParentTag(e,o)}expandToTag(e){this.selectionUtils.expandToTag(e)}},ToolsAPI:class extends Q{get methods(){return{getBlockTools:()=>Array.from(this.Editor.Tools.blockTools.values())}}},StylesAPI:class extends Q{get classes(){return{block:"cdx-block",inlineToolButton:"ce-inline-tool",inlineToolButtonActive:"ce-inline-tool--active",input:"cdx-input",loader:"cdx-loader",button:"cdx-button",settingsButton:"cdx-settings-button",settingsButtonActive:"cdx-settings-button--active"}}},ToolbarAPI:class extends Q{get methods(){return{close:()=>this.close(),open:()=>this.open(),toggleBlockSettings:e=>this.toggleBlockSettings(e),toggleToolbox:e=>this.toggleToolbox(e)}}open(){this.Editor.Toolbar.moveAndOpen()}close(){this.Editor.Toolbar.close()}toggleBlockSettings(e){if(-1===this.Editor.BlockManager.currentBlockIndex)return void C("Could't toggle the Toolbar because there is no block selected ","warn");e??!this.Editor.BlockSettings.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.open()):this.Editor.BlockSettings.close()}toggleToolbox(e){if(-1===this.Editor.BlockManager.currentBlockIndex)return void C("Could't toggle the Toolbox because there is no block selected ","warn");e??!this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()):this.Editor.Toolbar.toolbox.close()}},TooltipAPI:class extends Q{constructor({config:e,eventsDispatcher:o}){super({config:e,eventsDispatcher:o})}get methods(){return{show:(e,o,i)=>this.show(e,o,i),hide:()=>this.hide(),onHover:(e,o,i)=>this.onHover(e,o,i)}}show(e,o,i){eB(),null==eC||eC.show(e,o,i)}hide(){eT()}onHover(e,o,i){eS(e,o,i)}},UiAPI:class extends Q{get methods(){return{nodes:this.editorNodes}}get editorNodes(){return{wrapper:this.Editor.UI.nodes.wrapper,redactor:this.Editor.UI.nodes.redactor}}},BlockSettings:class extends Q{constructor(){super(...arguments),this.opened=!1,this.selection=new J,this.popover=null,this.close=()=>{this.opened&&(this.opened=!1,J.isAtEditor||this.selection.restore(),this.selection.clearSaved(),!this.Editor.CrossBlockSelection.isCrossBlockSelectionStarted&&this.Editor.BlockManager.currentBlock&&this.Editor.BlockSelection.unselectBlock(this.Editor.BlockManager.currentBlock),this.eventsDispatcher.emit(this.events.closed),this.popover&&(this.popover.off(eY.Closed,this.onPopoverClose),this.popover.destroy(),this.popover.getElement().remove(),this.popover=null))},this.onPopoverClose=()=>{this.close()}}get events(){return{opened:"block-settings-opened",closed:"block-settings-closed"}}get CSS(){return{settings:"ce-settings"}}get flipper(){var e;if(null!==this.popover)return"flipper"in this.popover?null==(e=this.popover)?void 0:e.flipper:void 0}make(){this.nodes.wrapper=Y.make("div",[this.CSS.settings]),this.nodes.wrapper.setAttribute("data-cy","block-tunes"),this.eventsDispatcher.on(en,this.close)}destroy(){this.removeAllNodes(),this.listeners.destroy(),this.eventsDispatcher.off(en,this.close)}async open(e=this.Editor.BlockManager.currentBlock){var o;this.opened=!0,this.selection.save(),this.Editor.BlockSelection.selectBlock(e),this.Editor.BlockSelection.clearCache();let{toolTunes:i,commonTunes:n}=e.getTunes();this.eventsDispatcher.emit(this.events.opened);let r=U()?to:e3;this.popover=new r({searchable:!0,items:await this.getTunesItems(e,n,i),scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:X.ui(eI.ui.popover,"Nothing found"),search:X.ui(eI.ui.popover,"Filter")}}),this.popover.on(eY.Closed,this.onPopoverClose),null==(o=this.nodes.wrapper)||o.append(this.popover.getElement()),this.popover.show()}getElement(){return this.nodes.wrapper}async getTunesItems(e,o,i){let n=[];void 0!==i&&i.length>0&&(n.push(...i),n.push({type:eh.Separator}));let r=Array.from(this.Editor.Tools.blockTools.values()),s=(await ea(e,r)).reduce((o,i)=>(i.toolbox.forEach(n=>{o.push({icon:n.icon,title:X.t(eI.toolNames,n.title),name:i.name,closeOnActivate:!0,onActivate:async()=>{let{BlockManager:o,Caret:r,Toolbar:s}=this.Editor,l=await o.convert(e,i.name,n.data);s.close(),r.setToBlock(l,r.positions.END)}})}),o),[]);return s.length>0&&(n.push({icon:eA,name:"convert-to",title:X.ui(eI.ui.popover,"Convert to"),children:{searchable:!0,items:s}}),n.push({type:eh.Separator})),n.push(...o),n.map(e=>this.resolveTuneAliases(e))}resolveTuneAliases(e){if(e.type===eh.Separator||e.type===eh.Html)return e;let o=function(e,o){let i={};return Object.keys(e).forEach(n=>{let r=o[n];void 0!==r?i[r]=e[n]:i[n]=e[n]}),i}(e,{label:"title"});return e.confirmation&&(o.confirmation=this.resolveTuneAliases(e.confirmation)),o}},Toolbar:class extends Q{constructor({config:e,eventsDispatcher:o}){super({config:e,eventsDispatcher:o}),this.toolboxInstance=null}get CSS(){return{toolbar:"ce-toolbar",content:"ce-toolbar__content",actions:"ce-toolbar__actions",actionsOpened:"ce-toolbar__actions--opened",toolbarOpened:"ce-toolbar--opened",openedToolboxHolderModifier:"codex-editor--toolbox-opened",plusButton:"ce-toolbar__plus",plusButtonShortcut:"ce-toolbar__plus-shortcut",settingsToggler:"ce-toolbar__settings-btn",settingsTogglerHidden:"ce-toolbar__settings-btn--hidden"}}get opened(){return this.nodes.wrapper.classList.contains(this.CSS.toolbarOpened)}get toolbox(){var e;return{opened:null==(e=this.toolboxInstance)?void 0:e.opened,close:()=>{var e;null==(e=this.toolboxInstance)||e.close()},open:()=>{if(null===this.toolboxInstance)return void E("toolbox.open() called before initialization is finished","warn");this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.toolboxInstance.open()},toggle:()=>{if(null===this.toolboxInstance)return void E("toolbox.toggle() called before initialization is finished","warn");this.toolboxInstance.toggle()},hasFocus:()=>{var e;return null==(e=this.toolboxInstance)?void 0:e.hasFocus()}}}get blockActions(){return{hide:()=>{this.nodes.actions.classList.remove(this.CSS.actionsOpened)},show:()=>{this.nodes.actions.classList.add(this.CSS.actionsOpened)}}}get blockTunesToggler(){return{hide:()=>this.nodes.settingsToggler.classList.add(this.CSS.settingsTogglerHidden),show:()=>this.nodes.settingsToggler.classList.remove(this.CSS.settingsTogglerHidden)}}toggleReadOnly(e){e?(this.destroy(),this.Editor.BlockSettings.destroy(),this.disableModuleBindings()):window.requestIdleCallback(()=>{this.drawUI(),this.enableModuleBindings()},{timeout:2e3})}moveAndOpen(e=this.Editor.BlockManager.currentBlock){let o;if(null===this.toolboxInstance)return void E("Can't open Toolbar since Editor initialization is not finished yet","warn");if(this.toolboxInstance.opened&&this.toolboxInstance.close(),this.Editor.BlockSettings.opened&&this.Editor.BlockSettings.close(),!e)return;this.hoveredBlock=e;let i=e.holder,{isMobile:n}=this.Editor.UI,r=e.firstInput,s=i.getBoundingClientRect(),l=void 0!==r?r.getBoundingClientRect():null,a=null!==l?l.top-s.top:null;if(n)o=i.offsetTop+i.offsetHeight;else if(void 0===r||(null!==a?a>20:void 0)){let n=parseInt(window.getComputedStyle(e.pluginsContent).paddingTop);o=i.offsetTop+n}else{let e=function(e){let o=window.getComputedStyle(e),i=parseFloat(o.fontSize),n=parseFloat(o.lineHeight)||1.2*i,r=parseFloat(o.paddingTop),s=parseFloat(o.borderTopWidth);return parseFloat(o.marginTop)+s+r+(n-i)/2+.8*i}(r),n=parseInt(window.getComputedStyle(this.nodes.plusButton).height,10);o=i.offsetTop+e-n+8+a}this.nodes.wrapper.style.top=`${Math.floor(o)}px`,1===this.Editor.BlockManager.blocks.length&&e.isEmpty?this.blockTunesToggler.hide():this.blockTunesToggler.show(),this.open()}close(){var e,o;this.Editor.ReadOnly.isEnabled||(null==(e=this.nodes.wrapper)||e.classList.remove(this.CSS.toolbarOpened),this.blockActions.hide(),null==(o=this.toolboxInstance)||o.close(),this.Editor.BlockSettings.close(),this.reset())}reset(){this.nodes.wrapper.style.top="unset"}open(e=!0){this.nodes.wrapper.classList.add(this.CSS.toolbarOpened),e?this.blockActions.show():this.blockActions.hide()}async make(){this.nodes.wrapper=Y.make("div",this.CSS.toolbar),["content","actions"].forEach(e=>{this.nodes[e]=Y.make("div",this.CSS[e])}),Y.append(this.nodes.wrapper,this.nodes.content),Y.append(this.nodes.content,this.nodes.actions),this.nodes.plusButton=Y.make("div",this.CSS.plusButton,{innerHTML:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M12 7V12M12 17V12M17 12H12M12 12H7"/></svg>'}),Y.append(this.nodes.actions,this.nodes.plusButton),this.readOnlyMutableListeners.on(this.nodes.plusButton,"click",()=>{eT(!0),this.plusButtonClicked()},!1);let e=Y.make("div");e.appendChild(document.createTextNode(X.ui(eI.ui.toolbar.toolbox,"Add"))),e.appendChild(Y.make("div",this.CSS.plusButtonShortcut,{textContent:"/"})),eS(this.nodes.plusButton,e,{hidingDelay:400}),this.nodes.settingsToggler=Y.make("span",this.CSS.settingsToggler,{innerHTML:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 7.29999H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 7.29999H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.30999 12H9.3"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 12H14.59"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M9.40999 16.7H9.4"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2.6" d="M14.6 16.7H14.59"/></svg>'}),Y.append(this.nodes.actions,this.nodes.settingsToggler);let o=Y.make("div"),i=Y.text(X.ui(eI.ui.blockTunes.toggler,"Click to tune")),n=await tp("Slash","/");o.appendChild(i),o.appendChild(Y.make("div",this.CSS.plusButtonShortcut,{textContent:F(`CMD + ${n}`)})),eS(this.nodes.settingsToggler,o,{hidingDelay:400}),Y.append(this.nodes.actions,this.makeToolbox()),Y.append(this.nodes.actions,this.Editor.BlockSettings.getElement()),Y.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}makeToolbox(){return this.toolboxInstance=new td({api:this.Editor.API.methods,tools:this.Editor.Tools.blockTools,i18nLabels:{filter:X.ui(eI.ui.popover,"Filter"),nothingFound:X.ui(eI.ui.popover,"Nothing found")}}),this.toolboxInstance.on(tc.Opened,()=>{this.Editor.UI.nodes.wrapper.classList.add(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(tc.Closed,()=>{this.Editor.UI.nodes.wrapper.classList.remove(this.CSS.openedToolboxHolderModifier)}),this.toolboxInstance.on(tc.BlockAdded,({block:e})=>{let{BlockManager:o,Caret:i}=this.Editor,n=o.getBlockById(e.id);0===n.inputs.length&&(n===o.lastBlock?(o.insertAtEnd(),i.setToBlock(o.lastBlock)):i.setToBlock(o.nextBlock))}),this.toolboxInstance.getElement()}plusButtonClicked(){var e;this.Editor.BlockManager.currentBlock=this.hoveredBlock,null==(e=this.toolboxInstance)||e.toggle()}enableModuleBindings(){this.readOnlyMutableListeners.on(this.nodes.settingsToggler,"mousedown",e=>{var o;e.stopPropagation(),this.settingsTogglerClicked(),null!=(o=this.toolboxInstance)&&o.opened&&this.toolboxInstance.close(),eT(!0)},!0),U()||this.eventsDispatcher.on(th,e=>{var o;this.Editor.BlockSettings.opened||null!=(o=this.toolboxInstance)&&o.opened||this.moveAndOpen(e.block)})}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}settingsTogglerClicked(){this.Editor.BlockManager.currentBlock=this.hoveredBlock,this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.BlockSettings.open(this.hoveredBlock)}drawUI(){this.Editor.BlockSettings.make(),this.make()}destroy(){this.removeAllNodes(),this.toolboxInstance&&this.toolboxInstance.destroy()}},InlineToolbar:class extends Q{constructor({config:e,eventsDispatcher:o}){super({config:e,eventsDispatcher:o}),this.CSS={inlineToolbar:"ce-inline-toolbar"},this.opened=!1,this.popover=null,this.toolbarVerticalMargin=U()?20:6,this.tools=new Map,window.requestIdleCallback(()=>{this.make()},{timeout:2e3})}async tryToShow(e=!1){e&&this.close(),this.allowedToShow()&&(await this.open(),this.Editor.Toolbar.close())}close(){var e,o;if(this.opened){for(let[e,o]of this.tools){let i=this.getToolShortcut(e.name);void 0!==i&&tr.remove(this.Editor.UI.nodes.redactor,i),T(o.clear)&&o.clear()}this.tools=new Map,this.reset(),this.opened=!1,null==(e=this.popover)||e.hide(),null==(o=this.popover)||o.destroy(),this.popover=null}}containsNode(e){return void 0!==this.nodes.wrapper&&this.nodes.wrapper.contains(e)}destroy(){var e;this.removeAllNodes(),null==(e=this.popover)||e.destroy(),this.popover=null}make(){this.nodes.wrapper=Y.make("div",[this.CSS.inlineToolbar,...this.isRtl?[this.Editor.UI.CSS.editorRtlFix]:[]]),this.nodes.wrapper.setAttribute("data-cy","inline-toolbar"),Y.append(this.Editor.UI.nodes.wrapper,this.nodes.wrapper)}async open(){var e;if(this.opened)return;this.opened=!0,null!==this.popover&&this.popover.destroy(),this.createToolsInstances();let o=await this.getPopoverItems();this.popover=new e6({items:o,scopeElement:this.Editor.API.methods.ui.nodes.redactor,messages:{nothingFound:X.ui(eI.ui.popover,"Nothing found"),search:X.ui(eI.ui.popover,"Filter")}}),this.move(this.popover.size.width),null==(e=this.nodes.wrapper)||e.append(this.popover.getElement()),this.popover.show()}move(e){let o=J.rect,i=this.Editor.UI.nodes.wrapper.getBoundingClientRect(),n={x:o.x-i.x,y:o.y+o.height-i.top+this.toolbarVerticalMargin};n.x+e+i.x>this.Editor.UI.contentRect.right&&(n.x=this.Editor.UI.contentRect.right-e-i.x),this.nodes.wrapper.style.left=Math.floor(n.x)+"px",this.nodes.wrapper.style.top=Math.floor(n.y)+"px"}reset(){this.nodes.wrapper.style.left="0",this.nodes.wrapper.style.top="0"}allowedToShow(){let e=J.get(),o=J.text;if(!e||!e.anchorNode||e.isCollapsed||o.length<1)return!1;let i=Y.isElement(e.anchorNode)?e.anchorNode:e.anchorNode.parentElement;if(null===i||null!==e&&["IMG","INPUT"].includes(i.tagName))return!1;let n=this.Editor.BlockManager.getBlock(e.anchorNode);return!!n&&!1!==this.getTools().some(e=>n.tool.inlineTools.has(e.name))&&null!==i.closest("[contenteditable]")}getTools(){let e=this.Editor.BlockManager.currentBlock;return e?Array.from(e.tool.inlineTools.values()).filter(e=>!(this.Editor.ReadOnly.isEnabled&&!0!==e.isReadOnlySupported)):[]}createToolsInstances(){this.tools=new Map,this.getTools().forEach(e=>{let o=e.create();this.tools.set(e,o)})}async getPopoverItems(){let e=[],o=0;for(let[i,n]of this.tools){let r=await n.render(),s=this.getToolShortcut(i.name);if(void 0!==s)try{this.enableShortcuts(i.name,s)}catch{}let l=void 0!==s?F(s):void 0,a=X.t(eI.toolNames,i.title||j(i.name));[r].flat().forEach(r=>{var s,c;let d={name:i.name,onActivate:()=>{this.toolClicked(n)},hint:{title:a,description:l}};if(Y.isElement(r)){let o={...d,element:r,type:eh.Html};if(T(n.renderActions)){let e=n.renderActions();o.children={isOpen:null==(s=n.checkState)?void 0:s.call(n,J.get()),isFlippable:!1,items:[{type:eh.Html,element:e}]}}else null==(c=n.checkState)||c.call(n,J.get());e.push(o)}else if(r.type===eh.Html)e.push({...d,...r,type:eh.Html});else if(r.type===eh.Separator)e.push({type:eh.Separator});else{let i={...d,...r,type:eh.Default};"children"in i&&0!==o&&e.push({type:eh.Separator}),e.push(i),"children"in i&&o<this.tools.size-1&&e.push({type:eh.Separator})}}),o++}return e}getToolShortcut(e){let{Tools:o}=this.Editor,i=o.inlineTools.get(e);return Array.from(o.internal.inlineTools.keys()).includes(e)?this.inlineTools[e][tg.Shortcut]:null==i?void 0:i.shortcut}enableShortcuts(e,o){tr.add({name:o,handler:o=>{var i;let{currentBlock:n}=this.Editor.BlockManager;n&&n.tool.enabledInlineTools&&(o.preventDefault(),null==(i=this.popover)||i.activateItemByName(e))},on:document})}toolClicked(e){var o;let i=J.range;null==(o=e.surround)||o.call(e,i),this.checkToolsState()}checkToolsState(){var e;null==(e=this.tools)||e.forEach(e=>{var o;null==(o=e.checkState)||o.call(e,J.get())})}get inlineTools(){let e={};return Array.from(this.Editor.Tools.inlineTools.entries()).forEach(([o,i])=>{e[o]=i.create()}),e}},BlockEvents:class extends Q{keydown(e){switch(this.beforeKeydownProcessing(e),e.keyCode){case 8:this.backspace(e);break;case 46:this.delete(e);break;case 13:this.enter(e);break;case 40:case 39:this.arrowRightAndDown(e);break;case 38:case 37:this.arrowLeftAndUp(e);break;case 9:this.tabPressed(e)}"/"!==e.key||e.ctrlKey||e.metaKey||this.slashPressed(e),"Slash"===e.code&&(e.ctrlKey||e.metaKey)&&(e.preventDefault(),this.commandSlashPressed())}beforeKeydownProcessing(e){this.needToolbarClosing(e)&&A(e.keyCode)&&(this.Editor.Toolbar.close(),e.ctrlKey||e.metaKey||e.altKey||e.shiftKey||this.Editor.BlockSelection.clearSelection(e))}keyup(e){e.shiftKey||this.Editor.UI.checkEmptiness()}dragOver(e){this.Editor.BlockManager.getBlockByChildNode(e.target).dropTarget=!0}dragLeave(e){this.Editor.BlockManager.getBlockByChildNode(e.target).dropTarget=!1}handleCommandC(e){let{BlockSelection:o}=this.Editor;o.anyBlockSelected&&o.copySelectedBlocks(e)}handleCommandX(e){let{BlockSelection:o,BlockManager:i,Caret:n}=this.Editor;o.anyBlockSelected&&o.copySelectedBlocks(e).then(()=>{let r=i.removeSelectedBlocks(),s=i.insertDefaultBlockAtIndex(r,!0);n.setToBlock(s,n.positions.START),o.clearSelection(e)})}tabPressed(e){let{InlineToolbar:o,Caret:i}=this.Editor;!o.opened&&(e.shiftKey?i.navigatePrevious(!0):i.navigateNext(!0))&&e.preventDefault()}commandSlashPressed(){this.Editor.BlockSelection.selectedBlocks.length>1||this.activateBlockSettings()}slashPressed(e){this.Editor.BlockManager.currentBlock.isEmpty&&(e.preventDefault(),this.Editor.Caret.insertContentAtCaretPosition("/"),this.activateToolbox())}enter(e){let{BlockManager:o,UI:i}=this.Editor,n=o.currentBlock;if(void 0===n||n.tool.isLineBreaksEnabled||i.someToolbarOpened&&i.someFlipperButtonFocused||e.shiftKey&&!$)return;let r=n;void 0!==n.currentInput&&tw(n.currentInput)&&!n.hasMedia?this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex):r=n.currentInput&&tE(n.currentInput)?this.Editor.BlockManager.insertDefaultBlockAtIndex(this.Editor.BlockManager.currentBlockIndex+1):this.Editor.BlockManager.split(),this.Editor.Caret.setToBlock(r),this.Editor.Toolbar.moveAndOpen(r),e.preventDefault()}backspace(e){let{BlockManager:o,Caret:i}=this.Editor,{currentBlock:n,previousBlock:r}=o;if(void 0!==n&&J.isCollapsed&&n.currentInput&&tw(n.currentInput)){if(e.preventDefault(),this.Editor.Toolbar.close(),n.currentInput!==n.firstInput)return void i.navigatePrevious();if(null!==r){if(r.isEmpty)return void o.removeBlock(r);if(n.isEmpty){o.removeBlock(n);let e=o.currentBlock;i.setToBlock(e,i.positions.END);return}ec(r,n)?this.mergeBlocks(r,n):i.setToBlock(r,i.positions.END)}}}delete(e){let{BlockManager:o,Caret:i}=this.Editor,{currentBlock:n,nextBlock:r}=o;if(J.isCollapsed&&tE(n.currentInput)){if(e.preventDefault(),this.Editor.Toolbar.close(),n.currentInput!==n.lastInput)return void i.navigateNext();if(null!==r){if(r.isEmpty)return void o.removeBlock(r);if(n.isEmpty){o.removeBlock(n),i.setToBlock(r,i.positions.START);return}ec(n,r)?this.mergeBlocks(n,r):i.setToBlock(r,i.positions.START)}}}mergeBlocks(e,o){let{BlockManager:i,Toolbar:n}=this.Editor;void 0!==e.lastInput&&(tC.focus(e.lastInput,!1),i.mergeBlocks(e,o).then(()=>{n.close()}))}arrowRightAndDown(e){let o=e_.usedKeys.includes(e.keyCode)&&(!e.shiftKey||9===e.keyCode);if(this.Editor.UI.someToolbarOpened&&o)return;this.Editor.Toolbar.close();let{currentBlock:i}=this.Editor.BlockManager,n=((null==i?void 0:i.currentInput)!==void 0?tE(i.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;return e.shiftKey&&40===e.keyCode&&n?void this.Editor.CrossBlockSelection.toggleBlockSelectedState():(40!==e.keyCode&&(39!==e.keyCode||this.isRtl)?this.Editor.Caret.navigatePrevious():this.Editor.Caret.navigateNext())?void e.preventDefault():void(N(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e))}arrowLeftAndUp(e){if(this.Editor.UI.someToolbarOpened){if(e_.usedKeys.includes(e.keyCode)&&(!e.shiftKey||9===e.keyCode))return;this.Editor.UI.closeAllToolbars()}this.Editor.Toolbar.close();let{currentBlock:o}=this.Editor.BlockManager,i=((null==o?void 0:o.currentInput)!==void 0?tw(o.currentInput):void 0)||this.Editor.BlockSelection.anyBlockSelected;return e.shiftKey&&38===e.keyCode&&i?void this.Editor.CrossBlockSelection.toggleBlockSelectedState(!1):(38!==e.keyCode&&(37!==e.keyCode||this.isRtl)?this.Editor.Caret.navigateNext():this.Editor.Caret.navigatePrevious())?void e.preventDefault():void(N(()=>{this.Editor.BlockManager.currentBlock&&this.Editor.BlockManager.currentBlock.updateCurrentInput()},20)(),this.Editor.BlockSelection.clearSelection(e))}needToolbarClosing(e){let o=13===e.keyCode&&this.Editor.Toolbar.toolbox.opened,i=13===e.keyCode&&this.Editor.BlockSettings.opened,n=13===e.keyCode&&this.Editor.InlineToolbar.opened,r=9===e.keyCode;return!(e.shiftKey||r||o||i||n)}activateToolbox(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.Toolbar.toolbox.open()}activateBlockSettings(){this.Editor.Toolbar.opened||this.Editor.Toolbar.moveAndOpen(),this.Editor.BlockSettings.opened||this.Editor.BlockSettings.open()}},BlockManager:class extends Q{constructor(){super(...arguments),this._currentBlockIndex=-1,this._blocks=null}get currentBlockIndex(){return this._currentBlockIndex}set currentBlockIndex(e){this._currentBlockIndex=e}get firstBlock(){return this._blocks[0]}get lastBlock(){return this._blocks[this._blocks.length-1]}get currentBlock(){return this._blocks[this.currentBlockIndex]}set currentBlock(e){this.currentBlockIndex=this.getBlockIndex(e)}get nextBlock(){return this.currentBlockIndex===this._blocks.length-1?null:this._blocks[this.currentBlockIndex+1]}get nextContentfulBlock(){return this.blocks.slice(this.currentBlockIndex+1).find(e=>!!e.inputs.length)}get previousContentfulBlock(){return this.blocks.slice(0,this.currentBlockIndex).reverse().find(e=>!!e.inputs.length)}get previousBlock(){return 0===this.currentBlockIndex?null:this._blocks[this.currentBlockIndex-1]}get blocks(){return this._blocks.array}get isEditorEmpty(){return this.blocks.every(e=>e.isEmpty)}prepare(){let e=new oD(this.Editor.UI.nodes.redactor);this._blocks=new Proxy(e,{set:oD.set,get:oD.get}),this.listeners.on(document,"copy",e=>this.Editor.BlockEvents.handleCommandC(e))}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}composeBlock({tool:e,data:o={},id:i,tunes:n={}}){let r=this.Editor.ReadOnly.isEnabled,s=new eu({id:i,data:o,tool:this.Editor.Tools.blockTools.get(e),api:this.Editor.API,readOnly:r,tunesData:n},this.eventsDispatcher);return r||window.requestIdleCallback(()=>{this.bindBlockEvents(s)},{timeout:2e3}),s}insert({id:e,tool:o=this.config.defaultBlock,data:i={},index:n,needToFocus:r=!0,replace:s=!1,tunes:l={}}={}){let a=n;void 0===a&&(a=this.currentBlockIndex+ +!s);let c=this.composeBlock({id:e,tool:o,data:i,tunes:l});return s&&this.blockDidMutated(oR,this.getBlockByIndex(a),{index:a}),this._blocks.insert(a,c,s),this.blockDidMutated(oj,c,{index:a}),r?this.currentBlockIndex=a:a<=this.currentBlockIndex&&this.currentBlockIndex++,c}insertMany(e,o=0){this._blocks.insertMany(e,o)}async update(e,o,i){if(!o&&!i)return e;let n=await e.data,r=this.composeBlock({id:e.id,tool:e.name,data:Object.assign({},n,o??{}),tunes:i??e.tunes}),s=this.getBlockIndex(e);return this._blocks.replace(s,r),this.blockDidMutated(oF,r,{index:s}),r}replace(e,o,i){let n=this.getBlockIndex(e);return this.insert({tool:o,data:i,index:n,replace:!0})}paste(e,o,i=!1){let n=this.insert({tool:e,replace:i});try{window.requestIdleCallback(()=>{n.call(ep.ON_PASTE,o)})}catch(o){E(`${e}: onPaste callback call is failed`,"error",o)}return n}insertDefaultBlockAtIndex(e,o=!1){let i=this.composeBlock({tool:this.config.defaultBlock});return this._blocks[e]=i,this.blockDidMutated(oj,i,{index:e}),o?this.currentBlockIndex=e:e<=this.currentBlockIndex&&this.currentBlockIndex++,i}insertAtEnd(){return this.currentBlockIndex=this.blocks.length-1,this.insert()}async mergeBlocks(e,o){let i;if(e.name===o.name&&e.mergeable){let n=await o.data;if(M(n))return void console.error("Could not merge Block. Failed to extract original Block data.");let[r]=ey([n],e.tool.sanitizeConfig);i=r}else e.mergeable&&es(o,"export")&&es(e,"import")&&(i=ed(ex(await o.exportDataAsString(),e.tool.sanitizeConfig),e.tool.conversionConfig));void 0!==i&&(await e.mergeWith(i),this.removeBlock(o),this.currentBlockIndex=this._blocks.indexOf(e))}removeBlock(e,o=!0){return new Promise(i=>{let n=this._blocks.indexOf(e);if(!this.validateIndex(n))throw Error("Can't find a Block to remove");e.destroy(),this._blocks.remove(n),this.blockDidMutated(oR,e,{index:n}),this.currentBlockIndex>=n&&this.currentBlockIndex--,this.blocks.length?0===n&&(this.currentBlockIndex=0):(this.unsetCurrentBlock(),o&&this.insert()),i()})}removeSelectedBlocks(){let e;for(let o=this.blocks.length-1;o>=0;o--)this.blocks[o].selected&&(this.removeBlock(this.blocks[o]),e=o);return e}removeAllBlocks(){for(let e=this.blocks.length-1;e>=0;e--)this._blocks.remove(e);this.unsetCurrentBlock(),this.insert(),this.currentBlock.firstInput.focus()}split(){let e=this.Editor.Caret.extractFragmentFromCaretPosition(),o=Y.make("div");o.appendChild(e);let i={text:Y.isEmpty(o)?"":o.innerHTML};return this.insert({data:i})}getBlockByIndex(e){return -1===e&&(e=this._blocks.length-1),this._blocks[e]}getBlockIndex(e){return this._blocks.indexOf(e)}getBlockById(e){return this._blocks.array.find(o=>o.id===e)}getBlock(e){Y.isElement(e)||(e=e.parentNode);let o=this._blocks.nodes,i=e.closest(`.${eu.CSS.wrapper}`),n=o.indexOf(i);if(n>=0)return this._blocks[n]}setCurrentBlockByChildNode(e){Y.isElement(e)||(e=e.parentNode);let o=e.closest(`.${eu.CSS.wrapper}`);if(!o)return;let i=o.closest(`.${this.Editor.UI.CSS.editorWrapper}`);if(null!=i&&i.isEqualNode(this.Editor.UI.nodes.wrapper))return this.currentBlockIndex=this._blocks.nodes.indexOf(o),this.currentBlock.updateCurrentInput(),this.currentBlock}getBlockByChildNode(e){if(!e||!(e instanceof Node))return;Y.isElement(e)||(e=e.parentNode);let o=e.closest(`.${eu.CSS.wrapper}`);return this.blocks.find(e=>e.holder===o)}swap(e,o){this._blocks.swap(e,o),this.currentBlockIndex=o}move(e,o=this.currentBlockIndex){return isNaN(e)||isNaN(o)?void E("Warning during 'move' call: incorrect indices provided.","warn"):this.validateIndex(e)&&this.validateIndex(o)?void(this._blocks.move(e,o),this.currentBlockIndex=e,this.blockDidMutated("block-moved",this.currentBlock,{fromIndex:o,toIndex:e})):void E("Warning during 'move' call: indices cannot be lower than 0 or greater than the amount of blocks.","warn")}async convert(e,o,i){if(!await e.save())throw Error("Could not convert Block. Failed to extract original Block data.");let n=this.Editor.Tools.blockTools.get(o);if(!n)throw Error(`Could not convert Block. Tool \xab${o}\xbb not found.`);let r=ed(ex(await e.exportDataAsString(),n.sanitizeConfig),n.conversionConfig,n.settings);return i&&(r=Object.assign(r,i)),this.replace(e,n.name,r)}unsetCurrentBlock(){this.currentBlockIndex=-1}async clear(e=!1){let o=new oH;this.blocks.forEach(e=>{o.add(async()=>{await this.removeBlock(e,!1)})}),await o.completed,this.unsetCurrentBlock(),e&&this.insert(),this.Editor.UI.checkEmptiness()}async destroy(){await Promise.all(this.blocks.map(e=>e.destroy()))}bindBlockEvents(e){let{BlockEvents:o}=this.Editor;this.readOnlyMutableListeners.on(e.holder,"keydown",e=>{o.keydown(e)}),this.readOnlyMutableListeners.on(e.holder,"keyup",e=>{o.keyup(e)}),this.readOnlyMutableListeners.on(e.holder,"dragover",e=>{o.dragOver(e)}),this.readOnlyMutableListeners.on(e.holder,"dragleave",e=>{o.dragLeave(e)}),e.on("didMutated",e=>this.blockDidMutated(oF,e,{index:this.getBlockIndex(e)}))}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}enableModuleBindings(){this.readOnlyMutableListeners.on(document,"cut",e=>this.Editor.BlockEvents.handleCommandX(e)),this.blocks.forEach(e=>{this.bindBlockEvents(e)})}validateIndex(e){return!(e<0||e>=this._blocks.length)}blockDidMutated(e,o,i){let n=new CustomEvent(e,{detail:{target:new q(o),...i}});return this.eventsDispatcher.emit(et,{event:n}),o}},BlockSelection:class extends Q{constructor(){super(...arguments),this.anyBlockSelectedCache=null,this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1}get sanitizerConfig(){return{p:{},h1:{},h2:{},h3:{},h4:{},h5:{},h6:{},ol:{},ul:{},li:{},br:!0,img:{src:!0,width:!0,height:!0},a:{href:!0},b:{},i:{},u:{}}}get allBlocksSelected(){let{BlockManager:e}=this.Editor;return e.blocks.every(e=>!0===e.selected)}set allBlocksSelected(e){let{BlockManager:o}=this.Editor;o.blocks.forEach(o=>{o.selected=e}),this.clearCache()}get anyBlockSelected(){let{BlockManager:e}=this.Editor;return null===this.anyBlockSelectedCache&&(this.anyBlockSelectedCache=e.blocks.some(e=>!0===e.selected)),this.anyBlockSelectedCache}get selectedBlocks(){return this.Editor.BlockManager.blocks.filter(e=>e.selected)}prepare(){this.selection=new J,tr.add({name:"CMD+A",handler:e=>{let{BlockManager:o,ReadOnly:i}=this.Editor;if(i.isEnabled){e.preventDefault(),this.selectAllBlocks();return}o.currentBlock&&this.handleCommandA(e)},on:this.Editor.UI.nodes.redactor})}toggleReadOnly(){J.get().removeAllRanges(),this.allBlocksSelected=!1}unSelectBlockByIndex(e){let{BlockManager:o}=this.Editor;(isNaN(e)?o.currentBlock:o.getBlockByIndex(e)).selected=!1,this.clearCache()}clearSelection(e,o=!1){let{BlockManager:i,Caret:n,RectangleSelection:r}=this.Editor;this.needToSelectAll=!1,this.nativeInputSelected=!1,this.readyToBlockSelection=!1;let s=e&&e instanceof KeyboardEvent,l=s&&A(e.keyCode);if(this.anyBlockSelected&&s&&l&&!J.isSelectionExists){let o=i.removeSelectedBlocks();i.insertDefaultBlockAtIndex(o,!0),n.setToBlock(i.currentBlock),N(()=>{let o=e.key;n.insertContentAtCaretPosition(o.length>1?"":o)},20)()}if(this.Editor.CrossBlockSelection.clear(e),!this.anyBlockSelected||r.isRectActivated())return void this.Editor.RectangleSelection.clearSelection();o&&this.selection.restore(),this.allBlocksSelected=!1}copySelectedBlocks(e){e.preventDefault();let o=Y.make("div");this.selectedBlocks.forEach(e=>{let i=ex(e.holder.innerHTML,this.sanitizerConfig),n=Y.make("p");n.innerHTML=i,o.appendChild(n)});let i=Array.from(o.childNodes).map(e=>e.textContent).join(`

`),n=o.innerHTML;return e.clipboardData.setData("text/plain",i),e.clipboardData.setData("text/html",n),Promise.all(this.selectedBlocks.map(e=>e.save())).then(o=>{try{e.clipboardData.setData(this.Editor.Paste.MIME_TYPE,JSON.stringify(o))}catch{}})}selectBlockByIndex(e){let{BlockManager:o}=this.Editor,i=o.getBlockByIndex(e);void 0!==i&&this.selectBlock(i)}selectBlock(e){this.selection.save(),J.get().removeAllRanges(),e.selected=!0,this.clearCache(),this.Editor.InlineToolbar.close()}unselectBlock(e){e.selected=!1,this.clearCache()}clearCache(){this.anyBlockSelectedCache=null}destroy(){tr.remove(this.Editor.UI.nodes.redactor,"CMD+A")}handleCommandA(e){if(this.Editor.RectangleSelection.clearSelection(),Y.isNativeInput(e.target)&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}let o=this.Editor.BlockManager.getBlock(e.target),i=o.inputs;if(i.length>1&&!this.readyToBlockSelection){this.readyToBlockSelection=!0;return}if(1===i.length&&!this.needToSelectAll){this.needToSelectAll=!0;return}this.needToSelectAll?(e.preventDefault(),this.selectAllBlocks(),this.needToSelectAll=!1,this.readyToBlockSelection=!1):this.readyToBlockSelection&&(e.preventDefault(),this.selectBlock(o),this.needToSelectAll=!0)}selectAllBlocks(){this.selection.save(),J.get().removeAllRanges(),this.allBlocksSelected=!0,this.Editor.InlineToolbar.close()}},Caret:oz,CrossBlockSelection:class extends Q{constructor(){super(...arguments),this.onMouseUp=()=>{this.listeners.off(document,"mouseover",this.onMouseOver),this.listeners.off(document,"mouseup",this.onMouseUp)},this.onMouseOver=e=>{let{BlockManager:o,BlockSelection:i}=this.Editor;if(null===e.relatedTarget&&null===e.target)return;let n=o.getBlockByChildNode(e.relatedTarget)||this.lastSelectedBlock,r=o.getBlockByChildNode(e.target);if(!(!n||!r)&&r!==n){if(n===this.firstSelectedBlock){J.get().removeAllRanges(),n.selected=!0,r.selected=!0,i.clearCache();return}if(r===this.firstSelectedBlock){n.selected=!1,r.selected=!1,i.clearCache();return}this.Editor.InlineToolbar.close(),this.toggleBlocksSelectedState(n,r),this.lastSelectedBlock=r}}}async prepare(){this.listeners.on(document,"mousedown",e=>{this.enableCrossBlockSelection(e)})}watchSelection(e){if(0!==e.button)return;let{BlockManager:o}=this.Editor;this.firstSelectedBlock=o.getBlock(e.target),this.lastSelectedBlock=this.firstSelectedBlock,this.listeners.on(document,"mouseover",this.onMouseOver),this.listeners.on(document,"mouseup",this.onMouseUp)}get isCrossBlockSelectionStarted(){return!!this.firstSelectedBlock&&!!this.lastSelectedBlock&&this.firstSelectedBlock!==this.lastSelectedBlock}toggleBlockSelectedState(e=!0){let{BlockManager:o,BlockSelection:i}=this.Editor;this.lastSelectedBlock||(this.lastSelectedBlock=this.firstSelectedBlock=o.currentBlock),this.firstSelectedBlock===this.lastSelectedBlock&&(this.firstSelectedBlock.selected=!0,i.clearCache(),J.get().removeAllRanges());let n=o.blocks.indexOf(this.lastSelectedBlock)+(e?1:-1),r=o.blocks[n];r&&(this.lastSelectedBlock.selected!==r.selected?r.selected=!0:this.lastSelectedBlock.selected=!1,i.clearCache(),this.lastSelectedBlock=r,this.Editor.InlineToolbar.close(),r.holder.scrollIntoView({block:"nearest"}))}clear(e){let{BlockManager:o,BlockSelection:i,Caret:n}=this.Editor,r=o.blocks.indexOf(this.firstSelectedBlock),s=o.blocks.indexOf(this.lastSelectedBlock);if(i.anyBlockSelected&&r>-1&&s>-1&&e&&e instanceof KeyboardEvent)switch(e.keyCode){case 40:case 39:default:n.setToBlock(o.blocks[Math.max(r,s)],n.positions.END);break;case 38:case 37:n.setToBlock(o.blocks[Math.min(r,s)],n.positions.START)}this.firstSelectedBlock=this.lastSelectedBlock=null}enableCrossBlockSelection(e){let{UI:o}=this.Editor;J.isCollapsed||this.Editor.BlockSelection.clearSelection(e),o.nodes.redactor.contains(e.target)?this.watchSelection(e):this.Editor.BlockSelection.clearSelection(e)}toggleBlocksSelectedState(e,o){let{BlockManager:i,BlockSelection:n}=this.Editor,r=i.blocks.indexOf(e),s=i.blocks.indexOf(o),l=e.selected!==o.selected;for(let a=Math.min(r,s);a<=Math.max(r,s);a++){let r=i.blocks[a];r!==this.firstSelectedBlock&&r!==(l?e:o)&&(i.blocks[a].selected=!i.blocks[a].selected,n.clearCache())}}},DragNDrop:class extends Q{constructor(){super(...arguments),this.isStartedAtEditor=!1}toggleReadOnly(e){e?this.disableModuleBindings():this.enableModuleBindings()}enableModuleBindings(){let{UI:e}=this.Editor;this.readOnlyMutableListeners.on(e.nodes.holder,"drop",async e=>{await this.processDrop(e)},!0),this.readOnlyMutableListeners.on(e.nodes.holder,"dragstart",()=>{this.processDragStart()}),this.readOnlyMutableListeners.on(e.nodes.holder,"dragover",e=>{this.processDragOver(e)},!0)}disableModuleBindings(){this.readOnlyMutableListeners.clearAll()}async processDrop(e){let{BlockManager:o,Paste:i,Caret:n}=this.Editor;e.preventDefault(),o.blocks.forEach(e=>{e.dropTarget=!1}),J.isAtEditor&&!J.isCollapsed&&this.isStartedAtEditor&&document.execCommand("delete"),this.isStartedAtEditor=!1;let r=o.setCurrentBlockByChildNode(e.target);if(r)this.Editor.Caret.setToBlock(r,n.positions.END);else{let e=o.setCurrentBlockByChildNode(o.lastBlock.holder);this.Editor.Caret.setToBlock(e,n.positions.END)}await i.processDataTransfer(e.dataTransfer,!0)}processDragStart(){J.isAtEditor&&!J.isCollapsed&&(this.isStartedAtEditor=!0),this.Editor.InlineToolbar.close()}processDragOver(e){e.preventDefault()}},ModificationsObserver:class extends Q{constructor({config:e,eventsDispatcher:o}){super({config:e,eventsDispatcher:o}),this.disabled=!1,this.batchingTimeout=null,this.batchingOnChangeQueue=new Map,this.batchTime=400,this.mutationObserver=new MutationObserver(e=>{this.redactorChanged(e)}),this.eventsDispatcher.on(et,e=>{this.particularBlockChanged(e.event)}),this.eventsDispatcher.on(eo,()=>{this.disable()}),this.eventsDispatcher.on(ei,()=>{this.enable()})}enable(){this.mutationObserver.observe(this.Editor.UI.nodes.redactor,{childList:!0,subtree:!0,characterData:!0,attributes:!0}),this.disabled=!1}disable(){this.mutationObserver.disconnect(),this.disabled=!0}particularBlockChanged(e){this.disabled||!T(this.config.onChange)||(this.batchingOnChangeQueue.set(`block:${e.detail.target.id}:event:${e.type}`,e),this.batchingTimeout&&clearTimeout(this.batchingTimeout),this.batchingTimeout=setTimeout(()=>{let e;e=1===this.batchingOnChangeQueue.size?this.batchingOnChangeQueue.values().next().value:Array.from(this.batchingOnChangeQueue.values()),this.config.onChange&&this.config.onChange(this.Editor.API.methods,e),this.batchingOnChangeQueue.clear()},this.batchTime))}redactorChanged(e){this.eventsDispatcher.emit(ee,{mutations:e})}},Paste:oU,ReadOnly:class extends Q{constructor(){super(...arguments),this.toolsDontSupportReadOnly=[],this.readOnlyEnabled=!1}get isEnabled(){return this.readOnlyEnabled}async prepare(){let{Tools:e}=this.Editor,{blockTools:o}=e,i=[];Array.from(o.entries()).forEach(([e,o])=>{o.isReadOnlySupported||i.push(e)}),this.toolsDontSupportReadOnly=i,this.config.readOnly&&i.length>0&&this.throwCriticalError(),this.toggle(this.config.readOnly,!0)}async toggle(e=!this.readOnlyEnabled,o=!1){e&&this.toolsDontSupportReadOnly.length>0&&this.throwCriticalError();let i=this.readOnlyEnabled;for(let o in this.readOnlyEnabled=e,this.Editor)this.Editor[o].toggleReadOnly&&this.Editor[o].toggleReadOnly(e);if(i===e||o)return this.readOnlyEnabled;this.Editor.ModificationsObserver.disable();let n=await this.Editor.Saver.save();return await this.Editor.BlockManager.clear(),await this.Editor.Renderer.render(n.blocks),this.Editor.ModificationsObserver.enable(),this.readOnlyEnabled}throwCriticalError(){throw new V(`To enable read-only mode all connected tools should support it. Tools ${this.toolsDontSupportReadOnly.join(", ")} don't support read-only mode.`)}},RectangleSelection:o$,Renderer:class extends Q{async render(e){return new Promise(o=>{let{Tools:i,BlockManager:n}=this.Editor;if(0===e.length)n.insert();else{let o=e.map(({type:e,data:o,tunes:r,id:s})=>{let l;!1===i.available.has(e)&&(C(`Tool \xab${e}\xbb is not found. Check 'tools' property at the Editor.js config.`,"warn"),o=this.composeStubDataForTool(e,o,s),e=i.stubTool);try{l=n.composeBlock({id:s,tool:e,data:o,tunes:r})}catch(a){E(`Block \xab${e}\xbb skipped because of plugins error`,"error",{data:o,error:a}),o=this.composeStubDataForTool(e,o,s),e=i.stubTool,l=n.composeBlock({id:s,tool:e,data:o,tunes:r})}return l});n.insertMany(o)}window.requestIdleCallback(()=>{o()},{timeout:2e3})})}composeStubDataForTool(e,o,i){let{Tools:n}=this.Editor,r=e;if(n.unavailable.has(e)){let o=n.unavailable.get(e).toolbox;void 0!==o&&void 0!==o[0].title&&(r=o[0].title)}return{savedData:{id:i,type:e,data:o},title:r}}},Saver:class extends Q{async save(){let{BlockManager:e,Tools:o}=this.Editor,i=e.blocks,n=[];try{i.forEach(e=>{n.push(this.getSavedData(e))});let e=await Promise.all(n),r=await ey(e,e=>o.blockTools.get(e).sanitizeConfig);return this.makeOutput(r)}catch(e){C("Saving failed due to the Error %o","error",e)}}async getSavedData(e){let o=await e.save(),i=o&&await e.validate(o.data);return{...o,isValid:i}}makeOutput(e){let o=[];return e.forEach(({id:e,tool:i,data:n,tunes:r,isValid:s})=>{if(!s)return void E(`Block \xab${i}\xbb skipped because saved data is invalid`);if(i===this.Editor.Tools.stubTool)return void o.push(n);let l={id:e,type:i,data:n,...!M(r)&&{tunes:r}};o.push(l)}),{time:+new Date,blocks:o,version:"2.31.0-rc.7"}}},Tools:ie,UI:class extends Q{constructor(){super(...arguments),this.isMobile=!1,this.contentRectCache=null,this.resizeDebouncer=D(()=>{this.windowResize()},200),this.selectionChangeDebounced=D(()=>{this.selectionChanged()},180),this.documentTouchedListener=e=>{this.documentTouched(e)}}get CSS(){return{editorWrapper:"codex-editor",editorWrapperNarrow:"codex-editor--narrow",editorZone:"codex-editor__redactor",editorZoneHidden:"codex-editor__redactor--hidden",editorEmpty:"codex-editor--empty",editorRtlFix:"codex-editor--rtl"}}get contentRect(){if(null!==this.contentRectCache)return this.contentRectCache;let e=this.nodes.wrapper.querySelector(`.${eu.CSS.content}`);return e?(this.contentRectCache=e.getBoundingClientRect(),this.contentRectCache):{width:650,left:0,right:0}}async prepare(){this.setIsMobile(),this.make(),this.loadStyles()}toggleReadOnly(e){e?this.unbindReadOnlySensitiveListeners():window.requestIdleCallback(()=>{this.bindReadOnlySensitiveListeners()},{timeout:2e3})}checkEmptiness(){let{BlockManager:e}=this.Editor;this.nodes.wrapper.classList.toggle(this.CSS.editorEmpty,e.isEditorEmpty)}get someToolbarOpened(){let{Toolbar:e,BlockSettings:o,InlineToolbar:i}=this.Editor;return!!(o.opened||i.opened||e.toolbox.opened)}get someFlipperButtonFocused(){return!!this.Editor.Toolbar.toolbox.hasFocus()||Object.entries(this.Editor).filter(([e,o])=>o.flipper instanceof e_).some(([e,o])=>o.flipper.hasFocus())}destroy(){this.nodes.holder.innerHTML="",this.unbindReadOnlyInsensitiveListeners()}closeAllToolbars(){let{Toolbar:e,BlockSettings:o,InlineToolbar:i}=this.Editor;o.close(),i.close(),e.toolbox.close()}setIsMobile(){let e=window.innerWidth<650;e!==this.isMobile&&this.eventsDispatcher.emit(en,{isEnabled:this.isMobile}),this.isMobile=e}make(){this.nodes.holder=Y.getHolder(this.config.holder),this.nodes.wrapper=Y.make("div",[this.CSS.editorWrapper,...this.isRtl?[this.CSS.editorRtlFix]:[]]),this.nodes.redactor=Y.make("div",this.CSS.editorZone),this.nodes.holder.offsetWidth<this.contentRect.width&&this.nodes.wrapper.classList.add(this.CSS.editorWrapperNarrow),this.nodes.redactor.style.paddingBottom=this.config.minHeight+"px",this.nodes.wrapper.appendChild(this.nodes.redactor),this.nodes.holder.appendChild(this.nodes.wrapper),this.bindReadOnlyInsensitiveListeners()}loadStyles(){let e="editor-js-styles";if(Y.get(e))return;let o=Y.make("style",null,{id:e,textContent:it.toString()});this.config.style&&!M(this.config.style)&&this.config.style.nonce&&o.setAttribute("nonce",this.config.style.nonce),Y.prepend(document.head,o)}bindReadOnlyInsensitiveListeners(){this.listeners.on(document,"selectionchange",this.selectionChangeDebounced),this.listeners.on(window,"resize",this.resizeDebouncer,{passive:!0}),this.listeners.on(this.nodes.redactor,"mousedown",this.documentTouchedListener,{capture:!0,passive:!0}),this.listeners.on(this.nodes.redactor,"touchstart",this.documentTouchedListener,{capture:!0,passive:!0})}unbindReadOnlyInsensitiveListeners(){this.listeners.off(document,"selectionchange",this.selectionChangeDebounced),this.listeners.off(window,"resize",this.resizeDebouncer),this.listeners.off(this.nodes.redactor,"mousedown",this.documentTouchedListener),this.listeners.off(this.nodes.redactor,"touchstart",this.documentTouchedListener)}bindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.on(this.nodes.redactor,"click",e=>{this.redactorClicked(e)},!1),this.readOnlyMutableListeners.on(document,"keydown",e=>{this.documentKeydown(e)},!0),this.readOnlyMutableListeners.on(document,"mousedown",e=>{this.documentClicked(e)},!0),this.watchBlockHoveredEvents(),this.enableInputsEmptyMark()}watchBlockHoveredEvents(){let e;this.readOnlyMutableListeners.on(this.nodes.redactor,"mousemove",R(o=>{let i=o.target.closest(".ce-block");this.Editor.BlockSelection.anyBlockSelected||i&&e!==i&&(e=i,this.eventsDispatcher.emit(th,{block:this.Editor.BlockManager.getBlockByChildNode(i)}))},20),{passive:!0})}unbindReadOnlySensitiveListeners(){this.readOnlyMutableListeners.clearAll()}windowResize(){this.contentRectCache=null,this.setIsMobile()}documentKeydown(e){switch(e.keyCode){case 13:this.enterPressed(e);break;case 8:case 46:this.backspacePressed(e);break;case 27:this.escapePressed(e);break;default:this.defaultBehaviour(e)}}defaultBehaviour(e){let{currentBlock:o}=this.Editor.BlockManager,i=e.target.closest(`.${this.CSS.editorWrapper}`),n=e.altKey||e.ctrlKey||e.metaKey||e.shiftKey;if(void 0!==o&&null===i)return void this.Editor.BlockEvents.keydown(e);i||o&&n||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close())}backspacePressed(e){let{BlockManager:o,BlockSelection:i,Caret:n}=this.Editor;if(i.anyBlockSelected&&!J.isSelectionExists){let r=o.removeSelectedBlocks(),s=o.insertDefaultBlockAtIndex(r,!0);n.setToBlock(s,n.positions.START),i.clearSelection(e),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation()}}escapePressed(e){this.Editor.BlockSelection.clearSelection(e),this.Editor.Toolbar.toolbox.opened?(this.Editor.Toolbar.toolbox.close(),this.Editor.Caret.setToBlock(this.Editor.BlockManager.currentBlock,this.Editor.Caret.positions.END)):this.Editor.BlockSettings.opened?this.Editor.BlockSettings.close():this.Editor.InlineToolbar.opened?this.Editor.InlineToolbar.close():this.Editor.Toolbar.close()}enterPressed(e){let{BlockManager:o,BlockSelection:i}=this.Editor;if(this.someToolbarOpened)return;let n=o.currentBlockIndex>=0;if(i.anyBlockSelected&&!J.isSelectionExists){i.clearSelection(e),e.preventDefault(),e.stopImmediatePropagation(),e.stopPropagation();return}if(!this.someToolbarOpened&&n&&"BODY"===e.target.tagName){let o=this.Editor.BlockManager.insert();e.preventDefault(),this.Editor.Caret.setToBlock(o),this.Editor.Toolbar.moveAndOpen(o)}this.Editor.BlockSelection.clearSelection(e)}documentClicked(e){var o,i;if(!e.isTrusted)return;let n=e.target;this.nodes.holder.contains(n)||J.isAtEditor||(this.Editor.BlockManager.unsetCurrentBlock(),this.Editor.Toolbar.close());let r=null==(o=this.Editor.BlockSettings.nodes.wrapper)?void 0:o.contains(n),s=null==(i=this.Editor.Toolbar.nodes.settingsToggler)?void 0:i.contains(n);if(this.Editor.BlockSettings.opened&&!(r||s)){this.Editor.BlockSettings.close();let e=this.Editor.BlockManager.getBlockByChildNode(n);this.Editor.Toolbar.moveAndOpen(e)}this.Editor.BlockSelection.clearSelection(e)}documentTouched(e){let o=e.target;if(o===this.nodes.redactor){let i=e instanceof MouseEvent?e.clientX:e.touches[0].clientX,n=e instanceof MouseEvent?e.clientY:e.touches[0].clientY;o=document.elementFromPoint(i,n)}try{this.Editor.BlockManager.setCurrentBlockByChildNode(o)}catch{this.Editor.RectangleSelection.isRectActivated()||this.Editor.Caret.setToTheLastBlock()}this.Editor.ReadOnly.isEnabled||this.Editor.Toolbar.moveAndOpen()}redactorClicked(e){if(!J.isCollapsed)return;let o=e.target,i=e.metaKey||e.ctrlKey;if(Y.isAnchor(o)&&i){var n;e.stopImmediatePropagation(),e.stopPropagation(),n=function(e){try{return new URL(e).href}catch{}return"//"===e.substring(0,2)?window.location.protocol+e:window.location.origin+e}(o.getAttribute("href")),window.open(n,"_blank");return}this.processBottomZoneClick(e)}processBottomZoneClick(e){let o=this.Editor.BlockManager.getBlockByIndex(-1),i=Y.offset(o.holder).bottom,n=e.pageY,{BlockSelection:r}=this.Editor;if(e.target instanceof Element&&e.target.isEqualNode(this.nodes.redactor)&&!r.anyBlockSelected&&i<n){e.stopImmediatePropagation(),e.stopPropagation();let{BlockManager:o,Caret:i,Toolbar:n}=this.Editor;o.lastBlock.tool.isDefault&&o.lastBlock.isEmpty||o.insertAtEnd(),i.setToTheLastBlock(),n.moveAndOpen(o.lastBlock)}}selectionChanged(){let{CrossBlockSelection:e,BlockSelection:o}=this.Editor,i=J.anchorElement;if(e.isCrossBlockSelectionStarted&&o.anyBlockSelected&&J.get().removeAllRanges(),!i){J.range||this.Editor.InlineToolbar.close();return}let n=i.closest(`.${eu.CSS.content}`);(null===n||n.closest(`.${J.CSS.editorWrapper}`)!==this.nodes.wrapper)&&(this.Editor.InlineToolbar.containsNode(i)||this.Editor.InlineToolbar.close(),"true"!==i.dataset.inlineToolbar)||(this.Editor.BlockManager.currentBlock||this.Editor.BlockManager.setCurrentBlockByChildNode(i),this.Editor.InlineToolbar.tryToShow(!0))}enableInputsEmptyMark(){function e(e){W(e.target)}this.readOnlyMutableListeners.on(this.nodes.wrapper,"input",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusin",e),this.readOnlyMutableListeners.on(this.nodes.wrapper,"focusout",e)}}};class ii{constructor(e){let o,i;this.moduleInstances={},this.eventsDispatcher=new Z,this.isReady=new Promise((e,n)=>{o=e,i=n}),Promise.resolve().then(async()=>{this.configuration=e,this.validate(),this.init(),await this.start(),await this.render();let{BlockManager:i,Caret:n,UI:r,ModificationsObserver:s}=this.moduleInstances;r.checkEmptiness(),s.enable(),!0===this.configuration.autofocus&&!0!==this.configuration.readOnly&&n.setToBlock(i.blocks[0],n.positions.START),o()}).catch(e=>{E(`Editor.js is not ready because of ${e}`,"error"),i(e)})}set configuration(e){var o,i;S(e)?this.config={...e}:this.config={holder:e},H(!!this.config.holderId,"config.holderId","config.holder"),this.config.holderId&&!this.config.holder&&(this.config.holder=this.config.holderId,this.config.holderId=null),null==this.config.holder&&(this.config.holder="editorjs"),this.config.logLevel||(this.config.logLevel=x.VERBOSE),w.logLevel=this.config.logLevel,H(!!this.config.initialBlock,"config.initialBlock","config.defaultBlock"),this.config.defaultBlock=this.config.defaultBlock||this.config.initialBlock||"paragraph",this.config.minHeight=void 0!==this.config.minHeight?this.config.minHeight:300;let n={type:this.config.defaultBlock,data:{}};this.config.placeholder=this.config.placeholder||!1,this.config.sanitizer=this.config.sanitizer||{p:!0,b:!0,a:!0},this.config.hideToolbar=!!this.config.hideToolbar&&this.config.hideToolbar,this.config.tools=this.config.tools||{},this.config.i18n=this.config.i18n||{},this.config.data=this.config.data||{blocks:[]},this.config.onReady=this.config.onReady||(()=>{}),this.config.onChange=this.config.onChange||(()=>{}),this.config.inlineToolbar=void 0===this.config.inlineToolbar||this.config.inlineToolbar,(M(this.config.data)||!this.config.data.blocks||0===this.config.data.blocks.length)&&(this.config.data={blocks:[n]}),this.config.readOnly=this.config.readOnly||!1,null!=(o=this.config.i18n)&&o.messages&&X.setDictionary(this.config.i18n.messages),this.config.i18n.direction=(null==(i=this.config.i18n)?void 0:i.direction)||"ltr"}get configuration(){return this.config}validate(){let{holderId:e,holder:o}=this.config;if(e&&o)throw Error("\xabholderId\xbb and \xabholder\xbb param can't assign at the same time.");if(I(o)&&!Y.get(o))throw Error(`element with ID \xab${o}\xbb is missing. Pass correct holder's ID.`);if(o&&S(o)&&!Y.isElement(o))throw Error("\xabholder\xbb value must be an Element node")}init(){this.constructModules(),this.configureModules()}async start(){await ["Tools","UI","BlockManager","Paste","BlockSelection","RectangleSelection","CrossBlockSelection","ReadOnly"].reduce((e,o)=>e.then(async()=>{try{await this.moduleInstances[o].prepare()}catch(e){if(e instanceof V)throw Error(e.message);E(`Module ${o} was skipped because of %o`,"warn",e)}}),Promise.resolve())}render(){return this.moduleInstances.Renderer.render(this.config.data.blocks)}constructModules(){Object.entries(io).forEach(([e,o])=>{try{this.moduleInstances[e]=new o({config:this.configuration,eventsDispatcher:this.eventsDispatcher})}catch(o){E("[constructModules]",`Module ${e} skipped because`,"error",o)}})}configureModules(){for(let e in this.moduleInstances)Object.prototype.hasOwnProperty.call(this.moduleInstances,e)&&(this.moduleInstances[e].state=this.getModulesDiff(e))}getModulesDiff(e){let o={};for(let i in this.moduleInstances)i!==e&&(o[i]=this.moduleInstances[i]);return o}}class ir{static get version(){return"2.31.0-rc.7"}constructor(e){let o=()=>{};S(e)&&T(e.onReady)&&(o=e.onReady);let i=new ii(e);this.isReady=i.isReady.then(()=>{this.exportAPI(i),o()})}exportAPI(e){["configuration"].forEach(o=>{this[o]=e[o]}),this.destroy=()=>{for(let o in Object.values(e.moduleInstances).forEach(e=>{T(e.destroy)&&e.destroy(),e.listeners.removeAll()}),null==eC||eC.destroy(),eC=null,e=null,this)Object.prototype.hasOwnProperty.call(this,o)&&delete this[o];Object.setPrototypeOf(this,null)},Object.setPrototypeOf(this,e.moduleInstances.API.methods),delete this.exportAPI,Object.entries({blocks:{clear:"clear",render:"render"},caret:{focus:"focus"},events:{on:"on",off:"off",emit:"emit"},saver:{save:"save"}}).forEach(([o,i])=>{Object.entries(i).forEach(([i,n])=>{this[n]=e.moduleInstances.API.methods[o][i]})})}}}}]);