"use strict";(self.webpackChunkglow_254=self.webpackChunkglow_254||[]).push([[4057],{4057:(e,t,n)=>{let r;n.d(t,{default:()=>ep});try{if("u">typeof document){var i=document.createElement("style");i.appendChild(document.createTextNode(".cdx-quote-icon svg{transform:rotate(180deg)}.cdx-quote{margin:0}.cdx-quote__text{min-height:158px;margin-bottom:10px}.cdx-quote [contentEditable=true][data-placeholder]:before{position:absolute;content:attr(data-placeholder);color:#707684;font-weight:400;opacity:0}.cdx-quote [contentEditable=true][data-placeholder]:empty:before{opacity:1}.cdx-quote [contentEditable=true][data-placeholder]:empty:focus:before{opacity:0}.cdx-quote-settings{display:flex}.cdx-quote-settings .cdx-settings-button{width:50%}")),document.head.appendChild(i)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}var o="u">typeof globalThis?globalThis:"u">typeof window?window:"u">typeof global?global:"u">typeof self?self:{},l={},a={},u={};Object.defineProperty(u,"__esModule",{value:!0}),u.allInputsSelector=function(){return"[contenteditable=true], textarea, input:not([type]), "+["text","password","email","number","search","tel","url"].map(function(e){return'input[type="'.concat(e,'"]')}).join(", ")},Object.defineProperty(a,"__esModule",{value:!0}),a.allInputsSelector=void 0,Object.defineProperty(a,"allInputsSelector",{enumerable:!0,get:function(){return u.allInputsSelector}});var c={},s={};Object.defineProperty(s,"__esModule",{value:!0}),s.isNativeInput=function(e){return!!e&&!!e.tagName&&["INPUT","TEXTAREA"].includes(e.tagName)},Object.defineProperty(c,"__esModule",{value:!0}),c.isNativeInput=void 0,Object.defineProperty(c,"isNativeInput",{enumerable:!0,get:function(){return s.isNativeInput}});var d={},p={};Object.defineProperty(p,"__esModule",{value:!0}),p.append=function(e,t){Array.isArray(t)?t.forEach(function(t){e.appendChild(t)}):e.appendChild(t)},Object.defineProperty(d,"__esModule",{value:!0}),d.append=void 0,Object.defineProperty(d,"append",{enumerable:!0,get:function(){return p.append}});var f={},g={};Object.defineProperty(g,"__esModule",{value:!0}),g.blockElements=function(){return["address","article","aside","blockquote","canvas","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","li","main","nav","noscript","ol","output","p","pre","ruby","section","table","tbody","thead","tr","tfoot","ul","video"]},Object.defineProperty(f,"__esModule",{value:!0}),f.blockElements=void 0,Object.defineProperty(f,"blockElements",{enumerable:!0,get:function(){return g.blockElements}});var b={},m={};Object.defineProperty(m,"__esModule",{value:!0}),m.calculateBaseline=function(e){var t=window.getComputedStyle(e),n=parseFloat(t.fontSize),r=parseFloat(t.lineHeight)||1.2*n,i=parseFloat(t.paddingTop),o=parseFloat(t.borderTopWidth);return parseFloat(t.marginTop)+o+i+(r-n)/2+.8*n},Object.defineProperty(b,"__esModule",{value:!0}),b.calculateBaseline=void 0,Object.defineProperty(b,"calculateBaseline",{enumerable:!0,get:function(){return m.calculateBaseline}});var y={},v={},h={},O={};Object.defineProperty(O,"__esModule",{value:!0}),O.isContentEditable=function(e){return"true"===e.contentEditable},Object.defineProperty(h,"__esModule",{value:!0}),h.isContentEditable=void 0,Object.defineProperty(h,"isContentEditable",{enumerable:!0,get:function(){return O.isContentEditable}}),Object.defineProperty(v,"__esModule",{value:!0}),v.canSetCaret=function(e){var t=!0;if((0,c.isNativeInput)(e))switch(e.type){case"file":case"checkbox":case"radio":case"hidden":case"submit":case"button":case"image":case"reset":t=!1}else t=(0,h.isContentEditable)(e);return t},Object.defineProperty(y,"__esModule",{value:!0}),y.canSetCaret=void 0,Object.defineProperty(y,"canSetCaret",{enumerable:!0,get:function(){return v.canSetCaret}});var _={},P={};function E(){let e={win:!1,mac:!1,x11:!1,linux:!1},t=Object.keys(e).find(e=>-1!==window.navigator.appVersion.toLowerCase().indexOf(e));return void 0!==t&&(e[t]=!0),e}function j(e){return null!=e&&""!==e&&("object"!=typeof e||Object.keys(e).length>0)}function C(e){return Object.prototype.toString.call(e).match(/\s([a-zA-Z]+)/)[1].toLowerCase()}function T(e){return"function"===C(e)||"asyncfunction"===C(e)}function M(e){return"object"===C(e)}let w=function(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}),n}(Object.freeze(Object.defineProperty({__proto__:null,PromiseQueue:class{constructor(){this.completed=Promise.resolve()}add(e){return new Promise((t,n)=>{this.completed=this.completed.then(e).then(t).catch(n)})}},beautifyShortcut:function(e){let t=E();return e=e.replace(/shift/gi,"⇧").replace(/backspace/gi,"⌫").replace(/enter/gi,"⏎").replace(/up/gi,"↑").replace(/left/gi,"→").replace(/down/gi,"↓").replace(/right/gi,"←").replace(/escape/gi,"⎋").replace(/insert/gi,"Ins").replace(/delete/gi,"␡").replace(/\+/gi,"+"),e=t.mac?e.replace(/ctrl|cmd/gi,"⌘").replace(/alt/gi,"⌥"):e.replace(/cmd/gi,"Ctrl").replace(/windows/gi,"WIN")},cacheable:function(e,t,n){let r=void 0!==n.value?"value":"get",i=n[r],o=`#${t}Cache`;if(n[r]=function(...e){return void 0===this[o]&&(this[o]=i.apply(this,e)),this[o]},"get"===r&&n.set){let t=n.set;n.set=function(n){delete e[o],t.apply(this,n)}}return n},capitalize:function(e){return e[0].toUpperCase()+e.slice(1)},copyTextToClipboard:function(e){let t=document.createElement("div");t.style.position="absolute",t.style.left="-999px",t.style.bottom="-999px",t.innerHTML=e,document.body.appendChild(t);let n=window.getSelection(),r=document.createRange();if(r.selectNode(t),null===n)throw Error("Cannot copy text to clipboard");n.removeAllRanges(),n.addRange(r),document.execCommand("copy"),document.body.removeChild(t)},debounce:function(e,t,n){let r;return(...i)=>{let o=this,l=!0===n&&void 0!==r;window.clearTimeout(r),r=window.setTimeout(()=>{r=void 0,!0!==n&&e.apply(o,i)},t),l&&e.apply(o,i)}},deepMerge:function e(t,...n){if(!n.length)return t;let r=n.shift();if(M(t)&&M(r))for(let n in r)M(r[n])?(void 0===t[n]&&Object.assign(t,{[n]:{}}),e(t[n],r[n])):Object.assign(t,{[n]:r[n]});return e(t,...n)},deprecationAssert:function(e,t,n){let r=`\xab${t}\xbb is deprecated and will be removed in the next major release. Please use the \xab${n}\xbb instead.`;e&&console.warn(r)},getUserOS:E,getValidUrl:function(e){try{return new URL(e).href}catch{}return"//"===e.substring(0,2)?window.location.protocol+e:window.location.origin+e},isBoolean:function(e){return"boolean"===C(e)},isClass:function(e){return T(e)&&/^\s*class\s+/.test(e.toString())},isEmpty:function(e){return!j(e)},isFunction:T,isIosDevice:()=>"u">typeof window&&null!==window.navigator&&j(window.navigator.platform)&&(/iP(ad|hone|od)/.test(window.navigator.platform)||"MacIntel"===window.navigator.platform&&window.navigator.maxTouchPoints>1),isNumber:function(e){return"number"===C(e)},isObject:M,isPrintableKey:function(e){return e>47&&e<58||32===e||13===e||229===e||e>64&&e<91||e>95&&e<112||e>185&&e<193||e>218&&e<223},isPromise:function(e){return Promise.resolve(e)===e},isString:function(e){return"string"===C(e)},isUndefined:function(e){return"undefined"===C(e)},keyCodes:{BACKSPACE:8,TAB:9,ENTER:13,SHIFT:16,CTRL:17,ALT:18,ESC:27,SPACE:32,LEFT:37,UP:38,DOWN:40,RIGHT:39,DELETE:46,META:91,SLASH:191},mouseButtons:{LEFT:0,WHEEL:1,RIGHT:2,BACKWARD:3,FORWARD:4},notEmpty:j,throttle:function(e,t,n){let r,i,o,l=null,a=0;n||(n={});let u=function(){a=!1===n.leading?0:Date.now(),l=null,o=e.apply(r,i),null===l&&(r=i=null)};return function(){let c=Date.now();a||!1!==n.leading||(a=c);let s=t-(c-a);return r=this,i=arguments,s<=0||s>t?(l&&(clearTimeout(l),l=null),a=c,o=e.apply(r,i),null===l&&(r=i=null)):l||!1===n.trailing||(l=setTimeout(u,s)),o}},typeOf:C},Symbol.toStringTag,{value:"Module"})));Object.defineProperty(P,"__esModule",{value:!0}),P.containsOnlyInlineElements=function(e){(0,w.isString)(e)?(t=document.createElement("div")).innerHTML=e:t=e;var t,n=function(e){return!(0,f.blockElements)().includes(e.tagName.toLowerCase())&&Array.from(e.children).every(n)};return Array.from(t.children).every(n)},Object.defineProperty(_,"__esModule",{value:!0}),_.containsOnlyInlineElements=void 0,Object.defineProperty(_,"containsOnlyInlineElements",{enumerable:!0,get:function(){return P.containsOnlyInlineElements}});var L={},k={},N={},A={};Object.defineProperty(A,"__esModule",{value:!0}),A.make=function(e,t,n){void 0===t&&(t=null),void 0===n&&(n={});var r,i=document.createElement(e);if(Array.isArray(t)){var o=t.filter(function(e){return void 0!==e});(r=i.classList).add.apply(r,o)}else null!==t&&i.classList.add(t);for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(i[l]=n[l]);return i},Object.defineProperty(N,"__esModule",{value:!0}),N.make=void 0,Object.defineProperty(N,"make",{enumerable:!0,get:function(){return A.make}}),Object.defineProperty(k,"__esModule",{value:!0}),k.fragmentToString=function(e){var t=(0,N.make)("div");return t.appendChild(e),t.innerHTML},Object.defineProperty(L,"__esModule",{value:!0}),L.fragmentToString=void 0,Object.defineProperty(L,"fragmentToString",{enumerable:!0,get:function(){return k.fragmentToString}});var S={},x={};Object.defineProperty(x,"__esModule",{value:!0}),x.getContentLength=function(e){var t,n;return(0,c.isNativeInput)(e)?e.value.length:e.nodeType===Node.TEXT_NODE?e.length:null!=(n=null==(t=e.textContent)?void 0:t.length)?n:0},Object.defineProperty(S,"__esModule",{value:!0}),S.getContentLength=void 0,Object.defineProperty(S,"getContentLength",{enumerable:!0,get:function(){return x.getContentLength}});var I={},B={},D=o&&o.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(B,"__esModule",{value:!0}),B.getDeepestBlockElements=function e(t){return(0,_.containsOnlyInlineElements)(t)?[t]:Array.from(t.children).reduce(function(t,n){return D(D([],t,!0),e(n),!0)},[])},Object.defineProperty(I,"__esModule",{value:!0}),I.getDeepestBlockElements=void 0,Object.defineProperty(I,"getDeepestBlockElements",{enumerable:!0,get:function(){return B.getDeepestBlockElements}});var R={},H={},F={},q={};Object.defineProperty(q,"__esModule",{value:!0}),q.isLineBreakTag=function(e){return["BR","WBR"].includes(e.tagName)},Object.defineProperty(F,"__esModule",{value:!0}),F.isLineBreakTag=void 0,Object.defineProperty(F,"isLineBreakTag",{enumerable:!0,get:function(){return q.isLineBreakTag}});var U={},W={};Object.defineProperty(W,"__esModule",{value:!0}),W.isSingleTag=function(e){return["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"].includes(e.tagName)},Object.defineProperty(U,"__esModule",{value:!0}),U.isSingleTag=void 0,Object.defineProperty(U,"isSingleTag",{enumerable:!0,get:function(){return W.isSingleTag}}),Object.defineProperty(H,"__esModule",{value:!0}),H.getDeepestNode=function e(t,n){void 0===n&&(n=!1);var r=n?"lastChild":"firstChild",i=n?"previousSibling":"nextSibling";if(t.nodeType===Node.ELEMENT_NODE&&t[r]){var o=t[r];if((0,U.isSingleTag)(o)&&!(0,c.isNativeInput)(o)&&!(0,F.isLineBreakTag)(o))if(o[i])o=o[i];else{if(null===o.parentNode||!o.parentNode[i])return o.parentNode;o=o.parentNode[i]}return e(o,n)}return t},Object.defineProperty(R,"__esModule",{value:!0}),R.getDeepestNode=void 0,Object.defineProperty(R,"getDeepestNode",{enumerable:!0,get:function(){return H.getDeepestNode}});var $={},G={},z=o&&o.__spreadArray||function(e,t,n){if(n||2==arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(G,"__esModule",{value:!0}),G.findAllInputs=function(e){return Array.from(e.querySelectorAll((0,a.allInputsSelector)())).reduce(function(e,t){return(0,c.isNativeInput)(t)||(0,_.containsOnlyInlineElements)(t)?z(z([],e,!0),[t],!1):z(z([],e,!0),(0,I.getDeepestBlockElements)(t),!0)},[])},Object.defineProperty($,"__esModule",{value:!0}),$.findAllInputs=void 0,Object.defineProperty($,"findAllInputs",{enumerable:!0,get:function(){return G.findAllInputs}});var K={},Q={};Object.defineProperty(Q,"__esModule",{value:!0}),Q.isCollapsedWhitespaces=function(e){return!/[^\t\n\r ]/.test(e)},Object.defineProperty(K,"__esModule",{value:!0}),K.isCollapsedWhitespaces=void 0,Object.defineProperty(K,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return Q.isCollapsedWhitespaces}});var X={},V={};Object.defineProperty(V,"__esModule",{value:!0}),V.isElement=function(e){return!(0,w.isNumber)(e)&&!!e&&!!e.nodeType&&e.nodeType===Node.ELEMENT_NODE},Object.defineProperty(X,"__esModule",{value:!0}),X.isElement=void 0,Object.defineProperty(X,"isElement",{enumerable:!0,get:function(){return V.isElement}});var Y={},Z={},J={},ee={};Object.defineProperty(ee,"__esModule",{value:!0}),ee.isLeaf=function(e){return null!==e&&0===e.childNodes.length},Object.defineProperty(J,"__esModule",{value:!0}),J.isLeaf=void 0,Object.defineProperty(J,"isLeaf",{enumerable:!0,get:function(){return ee.isLeaf}});var et={},en={};Object.defineProperty(en,"__esModule",{value:!0}),en.isNodeEmpty=function(e,t){var n="";return(!(0,U.isSingleTag)(e)||!!(0,F.isLineBreakTag)(e))&&((0,X.isElement)(e)&&(0,c.isNativeInput)(e)?n=e.value:null!==e.textContent&&(n=e.textContent.replace("​","")),void 0!==t&&(n=n.replace(RegExp(t,"g"),"")),0===n.trim().length)},Object.defineProperty(et,"__esModule",{value:!0}),et.isNodeEmpty=void 0,Object.defineProperty(et,"isNodeEmpty",{enumerable:!0,get:function(){return en.isNodeEmpty}}),Object.defineProperty(Z,"__esModule",{value:!0}),Z.isEmpty=function(e,t){e.normalize();for(var n=[e];n.length>0;){var r=n.shift();if(r){if(e=r,(0,J.isLeaf)(e)&&!(0,et.isNodeEmpty)(e,t))return!1;n.push.apply(n,Array.from(e.childNodes))}}return!0},Object.defineProperty(Y,"__esModule",{value:!0}),Y.isEmpty=void 0,Object.defineProperty(Y,"isEmpty",{enumerable:!0,get:function(){return Z.isEmpty}});var er={},ei={};Object.defineProperty(ei,"__esModule",{value:!0}),ei.isFragment=function(e){return!(0,w.isNumber)(e)&&!!e&&!!e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE},Object.defineProperty(er,"__esModule",{value:!0}),er.isFragment=void 0,Object.defineProperty(er,"isFragment",{enumerable:!0,get:function(){return ei.isFragment}});var eo={},el={};Object.defineProperty(el,"__esModule",{value:!0}),el.isHTMLString=function(e){var t=(0,N.make)("div");return t.innerHTML=e,t.childElementCount>0},Object.defineProperty(eo,"__esModule",{value:!0}),eo.isHTMLString=void 0,Object.defineProperty(eo,"isHTMLString",{enumerable:!0,get:function(){return el.isHTMLString}});var ea={},eu={};Object.defineProperty(eu,"__esModule",{value:!0}),eu.offset=function(e){var t=e.getBoundingClientRect(),n=window.pageXOffset||document.documentElement.scrollLeft,r=window.pageYOffset||document.documentElement.scrollTop,i=t.top+r,o=t.left+n;return{top:i,left:o,bottom:i+t.height,right:o+t.width}},Object.defineProperty(ea,"__esModule",{value:!0}),ea.offset=void 0,Object.defineProperty(ea,"offset",{enumerable:!0,get:function(){return eu.offset}});var ec={},es={};Object.defineProperty(es,"__esModule",{value:!0}),es.prepend=function(e,t){Array.isArray(t)?(t=t.reverse()).forEach(function(t){return e.prepend(t)}):e.prepend(t)},Object.defineProperty(ec,"__esModule",{value:!0}),ec.prepend=void 0,Object.defineProperty(ec,"prepend",{enumerable:!0,get:function(){return es.prepend}}),Object.defineProperty(l,"__esModule",{value:!0}),l.prepend=l.offset=l.make=l.isLineBreakTag=l.isSingleTag=l.isNodeEmpty=l.isLeaf=l.isHTMLString=l.isFragment=l.isEmpty=l.isElement=l.isContentEditable=l.isCollapsedWhitespaces=l.findAllInputs=l.isNativeInput=l.allInputsSelector=l.getDeepestNode=l.getDeepestBlockElements=l.getContentLength=l.fragmentToString=l.containsOnlyInlineElements=l.canSetCaret=l.calculateBaseline=l.blockElements=l.append=void 0,Object.defineProperty(l,"allInputsSelector",{enumerable:!0,get:function(){return a.allInputsSelector}}),Object.defineProperty(l,"isNativeInput",{enumerable:!0,get:function(){return c.isNativeInput}}),Object.defineProperty(l,"append",{enumerable:!0,get:function(){return d.append}}),Object.defineProperty(l,"blockElements",{enumerable:!0,get:function(){return f.blockElements}}),Object.defineProperty(l,"calculateBaseline",{enumerable:!0,get:function(){return b.calculateBaseline}}),Object.defineProperty(l,"canSetCaret",{enumerable:!0,get:function(){return y.canSetCaret}}),Object.defineProperty(l,"containsOnlyInlineElements",{enumerable:!0,get:function(){return _.containsOnlyInlineElements}}),Object.defineProperty(l,"fragmentToString",{enumerable:!0,get:function(){return L.fragmentToString}}),Object.defineProperty(l,"getContentLength",{enumerable:!0,get:function(){return S.getContentLength}}),Object.defineProperty(l,"getDeepestBlockElements",{enumerable:!0,get:function(){return I.getDeepestBlockElements}}),Object.defineProperty(l,"getDeepestNode",{enumerable:!0,get:function(){return R.getDeepestNode}}),Object.defineProperty(l,"findAllInputs",{enumerable:!0,get:function(){return $.findAllInputs}}),Object.defineProperty(l,"isCollapsedWhitespaces",{enumerable:!0,get:function(){return K.isCollapsedWhitespaces}}),Object.defineProperty(l,"isContentEditable",{enumerable:!0,get:function(){return h.isContentEditable}}),Object.defineProperty(l,"isElement",{enumerable:!0,get:function(){return X.isElement}}),Object.defineProperty(l,"isEmpty",{enumerable:!0,get:function(){return Y.isEmpty}}),Object.defineProperty(l,"isFragment",{enumerable:!0,get:function(){return er.isFragment}}),Object.defineProperty(l,"isHTMLString",{enumerable:!0,get:function(){return eo.isHTMLString}}),Object.defineProperty(l,"isLeaf",{enumerable:!0,get:function(){return J.isLeaf}}),Object.defineProperty(l,"isNodeEmpty",{enumerable:!0,get:function(){return et.isNodeEmpty}}),Object.defineProperty(l,"isLineBreakTag",{enumerable:!0,get:function(){return F.isLineBreakTag}}),Object.defineProperty(l,"isSingleTag",{enumerable:!0,get:function(){return U.isSingleTag}}),Object.defineProperty(l,"make",{enumerable:!0,get:function(){return N.make}}),Object.defineProperty(l,"offset",{enumerable:!0,get:function(){return ea.offset}}),Object.defineProperty(l,"prepend",{enumerable:!0,get:function(){return ec.prepend}});var ed=((r=ed||{}).Left="left",r.Center="center",r);class ep{constructor({data:e,config:t,api:n,readOnly:r,block:i}){let{DEFAULT_ALIGNMENT:o}=ep;this.api=n,this.readOnly=r,this.quotePlaceholder=n.i18n.t((null==t?void 0:t.quotePlaceholder)??ep.DEFAULT_QUOTE_PLACEHOLDER),this.captionPlaceholder=n.i18n.t((null==t?void 0:t.captionPlaceholder)??ep.DEFAULT_CAPTION_PLACEHOLDER),this.data={text:e.text||"",caption:e.caption||"",alignment:Object.values(ed).includes(e.alignment)?e.alignment:(null==t?void 0:t.defaultAlignment)??o},this.css={baseClass:this.api.styles.block,wrapper:"cdx-quote",text:"cdx-quote__text",input:this.api.styles.input,caption:"cdx-quote__caption"},this.block=i}static get isReadOnlySupported(){return!0}static get toolbox(){return{icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 10.8182L9 10.8182C8.80222 10.8182 8.60888 10.7649 8.44443 10.665C8.27998 10.5651 8.15181 10.4231 8.07612 10.257C8.00043 10.0909 7.98063 9.90808 8.01922 9.73174C8.0578 9.55539 8.15304 9.39341 8.29289 9.26627C8.43275 9.13913 8.61093 9.05255 8.80491 9.01747C8.99889 8.98239 9.19996 9.00039 9.38268 9.0692C9.56541 9.13801 9.72159 9.25453 9.83147 9.40403C9.94135 9.55353 10 9.72929 10 9.90909L10 12.1818C10 12.664 9.78929 13.1265 9.41421 13.4675C9.03914 13.8084 8.53043 14 8 14"/><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 10.8182L15 10.8182C14.8022 10.8182 14.6089 10.7649 14.4444 10.665C14.28 10.5651 14.1518 10.4231 14.0761 10.257C14.0004 10.0909 13.9806 9.90808 14.0192 9.73174C14.0578 9.55539 14.153 9.39341 14.2929 9.26627C14.4327 9.13913 14.6109 9.05255 14.8049 9.01747C14.9989 8.98239 15.2 9.00039 15.3827 9.0692C15.5654 9.13801 15.7216 9.25453 15.8315 9.40403C15.9414 9.55353 16 9.72929 16 9.90909L16 12.1818C16 12.664 15.7893 13.1265 15.4142 13.4675C15.0391 13.8084 14.5304 14 14 14"/></svg>',title:"Quote"}}static get contentless(){return!0}static get enableLineBreaks(){return!0}static get DEFAULT_QUOTE_PLACEHOLDER(){return"Enter a quote"}static get DEFAULT_CAPTION_PLACEHOLDER(){return"Enter a caption"}static get DEFAULT_ALIGNMENT(){return"left"}static get conversionConfig(){return{import:"text",export:function(e){return e.caption?`${e.text} — ${e.caption}`:e.text}}}get CSS(){return{baseClass:this.api.styles.block,wrapper:"cdx-quote",text:"cdx-quote__text",input:this.api.styles.input,caption:"cdx-quote__caption"}}get settings(){return[{name:"left",icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M17 7L5 7"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M17 17H5"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M13 12L5 12"/></svg>'},{name:"center",icon:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M18 7L6 7"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M18 17H6"/><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M16 12L8 12"/></svg>'}]}render(){let e=l.make("blockquote",[this.css.baseClass,this.css.wrapper]),t=l.make("div",[this.css.input,this.css.text],{contentEditable:!this.readOnly,innerHTML:this.data.text}),n=l.make("div",[this.css.input,this.css.caption],{contentEditable:!this.readOnly,innerHTML:this.data.caption});return t.dataset.placeholder=this.quotePlaceholder,n.dataset.placeholder=this.captionPlaceholder,e.appendChild(t),e.appendChild(n),e}save(e){let t=e.querySelector(`.${this.css.text}`),n=e.querySelector(`.${this.css.caption}`);return Object.assign(this.data,{text:(null==t?void 0:t.innerHTML)??"",caption:(null==n?void 0:n.innerHTML)??""})}static get sanitize(){return{text:{br:!0},caption:{br:!0},alignment:{}}}renderSettings(){return this.settings.map(e=>{let t;return{icon:e.icon,label:this.api.i18n.t(`Align ${(t=e.name)&&t[0].toUpperCase()+t.slice(1)}`),onActivate:()=>this._toggleTune(e.name),isActive:this.data.alignment===e.name,closeOnActivate:!0}})}_toggleTune(e){this.data.alignment=e,this.block.dispatchChange()}}}}]);