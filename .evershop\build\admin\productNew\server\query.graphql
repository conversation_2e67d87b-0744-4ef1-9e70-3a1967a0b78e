{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsnt4: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsnt5: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsnt6: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsnt7: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsnt8: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsnt9: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsnta: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsntb: url(routeId: \"productNew\")\n", "e2bec704576eeb5e1116212e04e0c0aac": "\n  e3cb8f7smcvvsntc: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    groupId\n    variantGroupId\n    attributeIndex {\n      attributeId\n      optionId\n      optionText\n    }\n  }\n  e3cb8f7smcvvsntd: attributeGroups(filters: $variable_3cb8f7smcvvsnte) {\n    items {\n      groupId: attributeGroupId\n      groupName\n      attributes {\n        items {\n          attributeId\n          attributeName\n          attributeCode\n          type\n          isRequired\n          options {\n            optionId: attributeOptionId\n            optionText\n          }\n        }\n      }\n    }\n  }\n", "ed96058d56f34e8072a9c322ba752fa54": "\n  e3cb8f7smcvvsntf: url(routeId: \"productGrid\")\n", "e8119dcb9877efa7f2105ee0b2b709f8d": "\n  e3cb8f7smcvvsntg: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    productId\n    name\n    description\n    sku\n    taxClass\n    price {\n      regular {\n        value\n        currency\n      }\n    }\n    weight {\n      value\n      unit\n    }\n    category {\n      categoryId\n      path {\n        name\n      }\n    }\n  }\n  e3cb8f7smcvvsnth: setting {\n    weightUnit\n    storeCurrency\n  }\n  e3cb8f7smcvvsnti: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsntj: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsntk: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsntl: url(routeId: \"folderCreate\")\n  e3cb8f7smcvvsntm: taxClasses {\n    items {\n      value: taxClassId\n      text: name\n    }\n  }\n", "eb1c6da056ec0d90944e6faaec7e5e6a6": "\n  e3cb8f7smcvvsntn: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    inventory {\n      qty\n      stockAvailability\n      manageStock\n    }\n  }\n", "e24a9a7f39dd59a1a221d4166e4518796": "\n  e3cb8f7smcvvsnto: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    image {\n      id: uuid\n      url\n    }\n    gallery {\n      id: uuid\n      url\n    }\n  }\n  e3cb8f7smcvvsntp: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n", "efede09f558db11db307f9acd3e35cb10": "\n  e3cb8f7smcvvsntq: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    name\n  }\n  e3cb8f7smcvvsntr: url(routeId: \"productGrid\")\n", "eda183e4ce6652e0050886813ec74ad87": "\n  e3cb8f7smcvvsnts: product(id: \"getContextValue_J3Byb2R1Y3RJZCcsIG51bGw=\") {\n    urlKey\n    metaTitle\n    metaKeywords\n    metaDescription\n  }\n", "ebe94deb3ac09f698ab5e5b38a4c3d0e5": "\n  e3cb8f7smcvvsntt: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    status\n    visibility\n    category {\n      value: categoryId\n      label: name\n    }\n  }\n", "eca9cba7fd377f5591871ad851eb22600": "\n  e3cb8f7smcvvsntu: url(routeId: \"createProduct\")\n  e3cb8f7smcvvsntv: url(routeId: \"productGrid\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsntw: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsntx: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsnty: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsntz: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsnu0: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsnu1: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsnu2: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsnu3: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsnu4: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsnu5: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsnu6: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsnu7: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsnu8: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsnu9: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsnua: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsnub: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsnuc: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsnud: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsnue\n    count: $variable_3cb8f7smcvvsnuf\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsnug: textWidget(text: $variable_3cb8f7smcvvsnul, className: $variable_3cb8f7smcvvsnum) {\n    text\n    className\n  }\n  e3cb8f7smcvvsnuh: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnui: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnuj: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnuk: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsnun: basicMenuWidget(settings: $variable_3cb8f7smcvvsnuo) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "e2bec704576eeb5e1116212e04e0c0aac": {"values": {"variable_3cb8f7smcvvsnte": [{"key": "limit", "operation": "eq", "value": 1000}]}, "defs": [{"origin": "filters", "type": "[FilterInput!]", "alias": "variable_3cb8f7smcvvsnte"}]}, "ed96058d56f34e8072a9c322ba752fa54": {"values": {}, "defs": []}, "e8119dcb9877efa7f2105ee0b2b709f8d": {"values": {}, "defs": []}, "eb1c6da056ec0d90944e6faaec7e5e6a6": {"values": {}, "defs": []}, "e24a9a7f39dd59a1a221d4166e4518796": {"values": {}, "defs": []}, "efede09f558db11db307f9acd3e35cb10": {"values": {}, "defs": []}, "eda183e4ce6652e0050886813ec74ad87": {"values": {}, "defs": []}, "ebe94deb3ac09f698ab5e5b38a4c3d0e5": {"values": {}, "defs": []}, "eca9cba7fd377f5591871ad851eb22600": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsnue": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsnuf": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsnue"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsnuf"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsnul": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsnum": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsnul"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsnum"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsnuo": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsnuo"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsnt4"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsnt5"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsnt6"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsnt7"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsnt8"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsnt9"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsnta"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsntb"}], "e2bec704576eeb5e1116212e04e0c0aac": [{"origin": "product", "alias": "e3cb8f7smcvvsntc"}, {"origin": "groups", "alias": "e3cb8f7smcvvsntd"}], "ed96058d56f34e8072a9c322ba752fa54": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsntf"}], "e8119dcb9877efa7f2105ee0b2b709f8d": [{"origin": "product", "alias": "e3cb8f7smcvvsntg"}, {"origin": "setting", "alias": "e3cb8f7smcvvsnth"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnti"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsntj"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsntk"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsntl"}, {"origin": "productTaxClasses", "alias": "e3cb8f7smcvvsntm"}], "eb1c6da056ec0d90944e6faaec7e5e6a6": [{"origin": "product", "alias": "e3cb8f7smcvvsntn"}], "e24a9a7f39dd59a1a221d4166e4518796": [{"origin": "product", "alias": "e3cb8f7smcvvsnto"}, {"origin": "productImageUploadUrl", "alias": "e3cb8f7smcvvsntp"}], "efede09f558db11db307f9acd3e35cb10": [{"origin": "product", "alias": "e3cb8f7smcvvsntq"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsntr"}], "eda183e4ce6652e0050886813ec74ad87": [{"origin": "product", "alias": "e3cb8f7smcvvsnts"}], "ebe94deb3ac09f698ab5e5b38a4c3d0e5": [{"origin": "product", "alias": "e3cb8f7smcvvsntt"}], "eca9cba7fd377f5591871ad851eb22600": [{"origin": "action", "alias": "e3cb8f7smcvvsntu"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsntv"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsntw"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsntx"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsnty"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsntz"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnu0"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsnu1"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsnu2"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsnu3"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsnu4"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsnu5"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsnu6"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsnu7"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsnu8"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsnu9"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsnua"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsnub"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsnuc"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsnud"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsnug"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnuh"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsnui"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsnuj"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsnuk"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsnun"}]}}