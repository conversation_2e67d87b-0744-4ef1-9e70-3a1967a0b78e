{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsnrp: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsnrq: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsnrr: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsnrs: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsnrt: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsnru: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsnrv: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsnrw: url(routeId: \"productNew\")\n", "e2bf1c5a7c5c672970aaea802ebe53c65": "\n  e3cb8f7smcvvsnrx: url(routeId: \"categoryGrid\")\n", "e9a0257b5330c2a1789de14e43ca0eee8": "\n  e3cb8f7smcvvsnry: category(id: \"getContextValue_ImNhdGVnb3J5SWQiLCBudWxs\") {\n    categoryId\n    name\n    description\n    status\n    parent {\n      categoryId\n      name\n      path {\n        name\n      }\n    }\n  }\n  e3cb8f7smcvvsnrz: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsns0: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsns1: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsns2: url(routeId: \"folderCreate\")\n", "e41819b8cedf6c4c33c007950e5a89ca7": "\n  e3cb8f7smcvvsns3: category(id: \"getContextValue_ImNhdGVnb3J5SWQiLCBudWxs\") {\n    image {\n      url\n    }\n  }\n  e3cb8f7smcvvsns4: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n", "e927993e3ab620feeddef69262065a46d": "\n  e3cb8f7smcvvsns5: category(id: \"getContextValue_ImNhdGVnb3J5SWQiLCBudWxs\") {\n    name\n  }\n  e3cb8f7smcvvsns6: url(routeId: \"categoryGrid\")\n", "e24aa9e50e89638cc5481224a5e321c4f": "\n  e3cb8f7smcvvsns7: category(id: \"getContextValue_J2NhdGVnb3J5SWQnLCBudWxs\") {\n    urlKey\n    metaTitle\n    metaKeywords\n    metaDescription\n  }\n", "eaa50d2593b352096a454c7f1394c741f": "\n  e3cb8f7smcvvsns8: category(id: \"getContextValue_ImNhdGVnb3J5SWQiLCBudWxs\") {\n    status\n    includeInNav\n    showProducts\n  }\n", "eaa3eb65c4b1c08ae7d813fe6fe568b70": "\n  e3cb8f7smcvvsns9: url(routeId: \"createCategory\")\n  e3cb8f7smcvvsnsa: url(routeId: \"categoryGrid\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsnsb: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsnsc: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsnsd: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsnse: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsnsf: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsnsg: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsnsh: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsnsi: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsnsj: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsnsk: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsnsl: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsnsm: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsnsn: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsnso: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsnsp: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsnsq: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsnsr: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsnss: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsnst\n    count: $variable_3cb8f7smcvvsnsu\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsnsv: textWidget(text: $variable_3cb8f7smcvvsnt0, className: $variable_3cb8f7smcvvsnt1) {\n    text\n    className\n  }\n  e3cb8f7smcvvsnsw: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnsx: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnsy: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnsz: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsnt2: basicMenuWidget(settings: $variable_3cb8f7smcvvsnt3) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "e2bf1c5a7c5c672970aaea802ebe53c65": {"values": {}, "defs": []}, "e9a0257b5330c2a1789de14e43ca0eee8": {"values": {}, "defs": []}, "e41819b8cedf6c4c33c007950e5a89ca7": {"values": {}, "defs": []}, "e927993e3ab620feeddef69262065a46d": {"values": {}, "defs": []}, "e24aa9e50e89638cc5481224a5e321c4f": {"values": {}, "defs": []}, "eaa50d2593b352096a454c7f1394c741f": {"values": {}, "defs": []}, "eaa3eb65c4b1c08ae7d813fe6fe568b70": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsnst": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsnsu": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsnst"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsnsu"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsnt0": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsnt1": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsnt0"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsnt1"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsnt3": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsnt3"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsnrp"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsnrq"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsnrr"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsnrs"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsnrt"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsnru"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsnrv"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsnrw"}], "e2bf1c5a7c5c672970aaea802ebe53c65": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsnrx"}], "e9a0257b5330c2a1789de14e43ca0eee8": [{"origin": "category", "alias": "e3cb8f7smcvvsnry"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnrz"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsns0"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsns1"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsns2"}], "e41819b8cedf6c4c33c007950e5a89ca7": [{"origin": "category", "alias": "e3cb8f7smcvvsns3"}, {"origin": "imageUploadUrl", "alias": "e3cb8f7smcvvsns4"}], "e927993e3ab620feeddef69262065a46d": [{"origin": "category", "alias": "e3cb8f7smcvvsns5"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsns6"}], "e24aa9e50e89638cc5481224a5e321c4f": [{"origin": "category", "alias": "e3cb8f7smcvvsns7"}], "eaa50d2593b352096a454c7f1394c741f": [{"origin": "category", "alias": "e3cb8f7smcvvsns8"}], "eaa3eb65c4b1c08ae7d813fe6fe568b70": [{"origin": "action", "alias": "e3cb8f7smcvvsns9"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsnsa"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsnsb"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsnsc"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsnsd"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnse"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnsf"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsnsg"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsnsh"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsnsi"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsnsj"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsnsk"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsnsl"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsnsm"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsnsn"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsnso"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsnsp"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsnsq"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsnsr"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsnss"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsnsv"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnsw"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsnsx"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsnsy"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsnsz"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsnt2"}]}}