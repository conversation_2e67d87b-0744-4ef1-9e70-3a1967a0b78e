CKEditor 5 autoformat feature
========================================

[![npm version](https://badge.fury.io/js/%40ckeditor%2Fckeditor5-autoformat.svg)](https://www.npmjs.com/package/@ckeditor/ckeditor5-autoformat)
[![Coverage Status](https://coveralls.io/repos/github/ckeditor/ckeditor5/badge.svg?branch=master)](https://coveralls.io/github/ckeditor/ckeditor5?branch=master)
[![Build Status](https://travis-ci.com/ckeditor/ckeditor5.svg?branch=master)](https://app.travis-ci.com/github/ckeditor/ckeditor5)
![Dependency Status](https://img.shields.io/librariesio/release/npm/@ckeditor/ckeditor5-autoformat)

This package implements the autoformatting feature for CKEditor 5. It allows styling text by typing sequences like `**bold this**`.

## Demo

Check out the [demo in the autoformat feature guide](https://ckeditor.com/docs/ckeditor5/latest/features/autoformat.html#demo).

## Documentation

See the [`@ckeditor/ckeditor5-autoformat` package](https://ckeditor.com/docs/ckeditor5/latest/api/autoformat.html) page in [CKEditor 5 documentation](https://ckeditor.com/docs/ckeditor5/latest/).

## License

Licensed under the terms of [GNU General Public License Version 2 or later](http://www.gnu.org/licenses/gpl.html). For full details about the license, please check the `LICENSE.md` file or [https://ckeditor.com/legal/ckeditor-oss-license](https://ckeditor.com/legal/ckeditor-oss-license).
