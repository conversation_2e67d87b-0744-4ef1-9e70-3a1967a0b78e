{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsowp: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsowq: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsowr: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsows: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsowt: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsowu: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsowv: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsoww: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsowx: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsowy: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsowz: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsox0: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsox1: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsox2: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsox3: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsox4: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsox5: version\n", "e54f04053d913790d95cc2f38cb69e5a5": "\n  e3cb8f7smcvvsox6: url(\n    routeId: \"updateWidget\"\n    params: [{key: \"id\", value: \"getContextValue_IndpZGdldFV1aWQi\"}]\n  )\n  e3cb8f7smcvvsox7: url(routeId: \"widgetGrid\")\n", "e790d919cff30e82dd0dff511634c3e28": "\n  e3cb8f7smcvvsox8: url(routeId: \"widgetGrid\")\n  e3cb8f7smcvvsox9: widgetType(code: \"getContextValue_J3R5cGUn\") {\n    code\n    description\n    settingComponent\n    defaultSetting\n  }\n", "e3955af68174aaeb60fc3e52454668857": "\n  e3cb8f7smcvvsoxa: widget(id: \"getContextValue_IndpZGdldElkIiwgbnVsbA==\") {\n    name\n    status\n    sortOrder\n    area\n    route\n  }\n  e3cb8f7smcvvsoxb: routes {\n    value: id\n    label: name\n    isApi\n    isAdmin\n    method\n  }\n", "e33b66faec0564c079be97eeed6f81b49": "\n  e3cb8f7smcvvsoxc: widget(id: \"getContextValue_IndpZGdldElkIiwgbnVsbA==\") {\n    name\n  }\n  e3cb8f7smcvvsoxd: url(routeId: \"widgetGrid\")\n", "e7d58999dcb83507f407f187e9e5b6455": "\n  e3cb8f7smcvvsoxe: widget(id: \"getContextValue_J3dpZGdldElkJywgbnVsbA==\") {\n    name\n    status\n    area\n    route\n    settings\n  }\n  e3cb8f7smcvvsoxf: widgetType(code: \"getContextValue_J3R5cGUnLCBudWxs\") {\n    code\n    name\n  }\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsoxg: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsoxh: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsoxi: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsoxj: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsoxk: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsoxl: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsoxm: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsoxn: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsoxo: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsoxp\n    count: $variable_3cb8f7smcvvsoxq\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsoxr: textWidget(text: $variable_3cb8f7smcvvsoxw, className: $variable_3cb8f7smcvvsoxx) {\n    text\n    className\n  }\n  e3cb8f7smcvvsoxs: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoxt: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoxu: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoxv: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsoxy: basicMenuWidget(settings: $variable_3cb8f7smcvvsoxz) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e54f04053d913790d95cc2f38cb69e5a5": {"values": {}, "defs": []}, "e790d919cff30e82dd0dff511634c3e28": {"values": {}, "defs": []}, "e3955af68174aaeb60fc3e52454668857": {"values": {}, "defs": []}, "e33b66faec0564c079be97eeed6f81b49": {"values": {}, "defs": []}, "e7d58999dcb83507f407f187e9e5b6455": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsoxp": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsoxq": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsoxp"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsoxq"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsoxw": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsoxx": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsoxw"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsoxx"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsoxz": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsoxz"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsowp"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsowq"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsowr"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsows"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsowt"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsowu"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsowv"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsoww"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsowx"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsowy"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsowz"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsox0"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsox1"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsox2"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsox3"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsox4"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsox5"}], "e54f04053d913790d95cc2f38cb69e5a5": [{"origin": "action", "alias": "e3cb8f7smcvvsox6"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsox7"}], "e790d919cff30e82dd0dff511634c3e28": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsox8"}, {"origin": "type", "alias": "e3cb8f7smcvvsox9"}], "e3955af68174aaeb60fc3e52454668857": [{"origin": "widget", "alias": "e3cb8f7smcvvsoxa"}, {"origin": "routes", "alias": "e3cb8f7smcvvsoxb"}], "e33b66faec0564c079be97eeed6f81b49": [{"origin": "page", "alias": "e3cb8f7smcvvsoxc"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsoxd"}], "e7d58999dcb83507f407f187e9e5b6455": [{"origin": "widget", "alias": "e3cb8f7smcvvsoxe"}, {"origin": "type", "alias": "e3cb8f7smcvvsoxf"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsoxg"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsoxh"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsoxi"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsoxj"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsoxk"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsoxl"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsoxm"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsoxn"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsoxo"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsoxr"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsoxs"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsoxt"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsoxu"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsoxv"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsoxy"}]}}