{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsoq6: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsoq7: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsoq8: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsoq9: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsoqa: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsoqb: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsoqc: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsoqd: url(routeId: \"productNew\")\n", "e5fa3cf2884625180e770de837d76661b": "\n  e3cb8f7smcvvsoqe: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    collections {\n      uuid\n      name\n      editUrl\n    }\n  }\n", "e5af9f41e486f72691ac78104a66c4039": "\n  e3cb8f7smcvvsoqf: url(\n    routeId: \"updateProduct\"\n    params: [{key: \"id\", value: \"getContextValue_InByb2R1Y3RVdWlkIg==\"}]\n  )\n  e3cb8f7smcvvsoqg: url(routeId: \"productGrid\")\n", "e5de46c7f884065cd5c562197b0c4c172": "\n  e3cb8f7smcvvsoqh: product(id: \"getContextValue_J3Byb2R1Y3RJZCcsIG51bGw=\") {\n    productId\n    uuid\n    variantGroup {\n      variantGroupId\n      attributes: variantAttributes {\n        attributeId\n        attributeCode\n        attributeName\n        options {\n          optionId\n          optionText\n        }\n      }\n      addItemApi\n    }\n  }\n  e3cb8f7smcvvsoqi: url(routeId: \"createVariantGroup\")\n  e3cb8f7smcvvsoqj: url(routeId: \"createProduct\")\n  e3cb8f7smcvvsoqk: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n", "e2bec704576eeb5e1116212e04e0c0aac": "\n  e3cb8f7smcvvsoql: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    groupId\n    variantGroupId\n    attributeIndex {\n      attributeId\n      optionId\n      optionText\n    }\n  }\n  e3cb8f7smcvvsoqm: attributeGroups(filters: $variable_3cb8f7smcvvsoqn) {\n    items {\n      groupId: attributeGroupId\n      groupName\n      attributes {\n        items {\n          attributeId\n          attributeName\n          attributeCode\n          type\n          isRequired\n          options {\n            optionId: attributeOptionId\n            optionText\n          }\n        }\n      }\n    }\n  }\n", "ed96058d56f34e8072a9c322ba752fa54": "\n  e3cb8f7smcvvsoqo: url(routeId: \"productGrid\")\n", "e8119dcb9877efa7f2105ee0b2b709f8d": "\n  e3cb8f7smcvvsoqp: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    productId\n    name\n    description\n    sku\n    taxClass\n    price {\n      regular {\n        value\n        currency\n      }\n    }\n    weight {\n      value\n      unit\n    }\n    category {\n      categoryId\n      path {\n        name\n      }\n    }\n  }\n  e3cb8f7smcvvsoqq: setting {\n    weightUnit\n    storeCurrency\n  }\n  e3cb8f7smcvvsoqr: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoqs: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoqt: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsoqu: url(routeId: \"folderCreate\")\n  e3cb8f7smcvvsoqv: taxClasses {\n    items {\n      value: taxClassId\n      text: name\n    }\n  }\n", "eb1c6da056ec0d90944e6faaec7e5e6a6": "\n  e3cb8f7smcvvsoqw: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    inventory {\n      qty\n      stockAvailability\n      manageStock\n    }\n  }\n", "e24a9a7f39dd59a1a221d4166e4518796": "\n  e3cb8f7smcvvsoqx: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    image {\n      id: uuid\n      url\n    }\n    gallery {\n      id: uuid\n      url\n    }\n  }\n  e3cb8f7smcvvsoqy: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n", "efede09f558db11db307f9acd3e35cb10": "\n  e3cb8f7smcvvsoqz: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    name\n  }\n  e3cb8f7smcvvsor0: url(routeId: \"productGrid\")\n", "eda183e4ce6652e0050886813ec74ad87": "\n  e3cb8f7smcvvsor1: product(id: \"getContextValue_J3Byb2R1Y3RJZCcsIG51bGw=\") {\n    urlKey\n    metaTitle\n    metaKeywords\n    metaDescription\n  }\n", "ebe94deb3ac09f698ab5e5b38a4c3d0e5": "\n  e3cb8f7smcvvsor2: product(id: \"getContextValue_InByb2R1Y3RJZCIsIG51bGw=\") {\n    status\n    visibility\n    category {\n      value: categoryId\n      label: name\n    }\n  }\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsor3: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsor4: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsor5: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsor6: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsor7: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsor8: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsor9: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsora: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsorb: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsorc: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsord: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsore: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsorf: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsorg: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsorh: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsori: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsorj: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsork: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsorl\n    count: $variable_3cb8f7smcvvsorm\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsorn: textWidget(text: $variable_3cb8f7smcvvsors, className: $variable_3cb8f7smcvvsort) {\n    text\n    className\n  }\n  e3cb8f7smcvvsoro: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsorp: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsorq: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsorr: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsoru: basicMenuWidget(settings: $variable_3cb8f7smcvvsorv) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "e5fa3cf2884625180e770de837d76661b": {"values": {}, "defs": []}, "e5af9f41e486f72691ac78104a66c4039": {"values": {}, "defs": []}, "e5de46c7f884065cd5c562197b0c4c172": {"values": {}, "defs": []}, "e2bec704576eeb5e1116212e04e0c0aac": {"values": {"variable_3cb8f7smcvvsoqn": [{"key": "limit", "operation": "eq", "value": 1000}]}, "defs": [{"origin": "filters", "type": "[FilterInput!]", "alias": "variable_3cb8f7smcvvsoqn"}]}, "ed96058d56f34e8072a9c322ba752fa54": {"values": {}, "defs": []}, "e8119dcb9877efa7f2105ee0b2b709f8d": {"values": {}, "defs": []}, "eb1c6da056ec0d90944e6faaec7e5e6a6": {"values": {}, "defs": []}, "e24a9a7f39dd59a1a221d4166e4518796": {"values": {}, "defs": []}, "efede09f558db11db307f9acd3e35cb10": {"values": {}, "defs": []}, "eda183e4ce6652e0050886813ec74ad87": {"values": {}, "defs": []}, "ebe94deb3ac09f698ab5e5b38a4c3d0e5": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsorl": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsorm": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsorl"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsorm"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsors": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsort": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsors"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsort"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsorv": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsorv"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsoq6"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsoq7"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsoq8"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsoq9"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsoqa"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsoqb"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsoqc"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsoqd"}], "e5fa3cf2884625180e770de837d76661b": [{"origin": "product", "alias": "e3cb8f7smcvvsoqe"}], "e5af9f41e486f72691ac78104a66c4039": [{"origin": "action", "alias": "e3cb8f7smcvvsoqf"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsoqg"}], "e5de46c7f884065cd5c562197b0c4c172": [{"origin": "product", "alias": "e3cb8f7smcvvsoqh"}, {"origin": "createVariantGroupApi", "alias": "e3cb8f7smcvvsoqi"}, {"origin": "createProductApi", "alias": "e3cb8f7smcvvsoqj"}, {"origin": "productImageUploadUrl", "alias": "e3cb8f7smcvvsoqk"}], "e2bec704576eeb5e1116212e04e0c0aac": [{"origin": "product", "alias": "e3cb8f7smcvvsoql"}, {"origin": "groups", "alias": "e3cb8f7smcvvsoqm"}], "ed96058d56f34e8072a9c322ba752fa54": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsoqo"}], "e8119dcb9877efa7f2105ee0b2b709f8d": [{"origin": "product", "alias": "e3cb8f7smcvvsoqp"}, {"origin": "setting", "alias": "e3cb8f7smcvvsoqq"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsoqr"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsoqs"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsoqt"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsoqu"}, {"origin": "productTaxClasses", "alias": "e3cb8f7smcvvsoqv"}], "eb1c6da056ec0d90944e6faaec7e5e6a6": [{"origin": "product", "alias": "e3cb8f7smcvvsoqw"}], "e24a9a7f39dd59a1a221d4166e4518796": [{"origin": "product", "alias": "e3cb8f7smcvvsoqx"}, {"origin": "productImageUploadUrl", "alias": "e3cb8f7smcvvsoqy"}], "efede09f558db11db307f9acd3e35cb10": [{"origin": "product", "alias": "e3cb8f7smcvvsoqz"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsor0"}], "eda183e4ce6652e0050886813ec74ad87": [{"origin": "product", "alias": "e3cb8f7smcvvsor1"}], "ebe94deb3ac09f698ab5e5b38a4c3d0e5": [{"origin": "product", "alias": "e3cb8f7smcvvsor2"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsor3"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsor4"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsor5"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsor6"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsor7"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsor8"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsor9"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsora"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsorb"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsorc"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsord"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsore"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsorf"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsorg"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsorh"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsori"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsorj"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsork"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsorn"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsoro"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsorp"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsorq"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsorr"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsoru"}]}}