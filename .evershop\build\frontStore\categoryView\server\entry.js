import React from 'react'; 
import ReactDOM from 'react-dom'; 
import { Area } from '@evershop/evershop/components/common';
import { renderHtml } from '@evershop/evershop/components/common';
import e9063e121a91d8fcd4205395b2308a655 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/all/SearchBox.js';
import eeeb13902327d5adad0b433439d6d3159 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/CategoryView.js';
import e37bf117a66a32da78e447515d5108d27 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/Filter.js';
import ef4a6efb4906690a3b7716fe6d4cb1e6b from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/General.js';
import e00986f8a5ffbca38d1fd0b45b7377f11 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/Pagination.js';
import e211c2fa2b6fa6913869a7eb5269d0bb7 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/Products.js';
import edf92e07655b2fc34cd7a13411d804140 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/categoryView/Sorting.js';
import e84f9b67788dd1391f5f95e066add1c5b from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/all/MiniCart.js';
import eab4e3642af32ca3183a4ba2d4b0482fe from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Breadcrumb.js';
import e1dcb7447781c87e8b5dbcd7126ec93ef from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Footer.js';
import e2b6e20920b7e0cce146f99c500ebc3f9 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/HeadTags.js';
import ee0a8efda73779da330342e1f92f72d87 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Layout.js';
import e1bb3a7d913f332202c6a8d5763b9e8fb from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Logo.js';
import ed239e378a9b62fdc82ddb5fbf70a1b80 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Notification.js';
import e1c7d051d98d0b4ad74a74e4272614474 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/customer/pages/frontStore/all/UserIcon.js';
import e4e223522edabcad19f6b9cbcb3b1746c from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/extensions/sample/dist/pages/frontStore/all/FreeShippingMessage.js';
import e9922f7b6522788416bdd8de4845d8832 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/themes/sample/dist/pages/all/EveryWhere.js';
import collection_products from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/CollectionProducts.js';
import text_block from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/TextBlock.js';
import basic_menu from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/BasicMenu.js';
export default renderHtml;
Area.defaultProps.components = {
  'icon-wrapper': {
    e9063e121a91d8fcd4205395b2308a655: {
      id: 'e9063e121a91d8fcd4205395b2308a655',
      sortOrder: 5,
      component: { default: e9063e121a91d8fcd4205395b2308a655 }
    },
    e84f9b67788dd1391f5f95e066add1c5b: {
      id: 'e84f9b67788dd1391f5f95e066add1c5b',
      sortOrder: 10,
      component: { default: e84f9b67788dd1391f5f95e066add1c5b }
    },
    e1c7d051d98d0b4ad74a74e4272614474: {
      id: 'e1c7d051d98d0b4ad74a74e4272614474',
      sortOrder: 30,
      component: { default: e1c7d051d98d0b4ad74a74e4272614474 }
    }
  },
  content: {
    eeeb13902327d5adad0b433439d6d3159: {
      id: 'eeeb13902327d5adad0b433439d6d3159',
      sortOrder: 10,
      component: { default: eeeb13902327d5adad0b433439d6d3159 }
    },
    ef4a6efb4906690a3b7716fe6d4cb1e6b: {
      id: 'ef4a6efb4906690a3b7716fe6d4cb1e6b',
      sortOrder: 5,
      component: { default: ef4a6efb4906690a3b7716fe6d4cb1e6b }
    },
    eab4e3642af32ca3183a4ba2d4b0482fe: {
      id: 'eab4e3642af32ca3183a4ba2d4b0482fe',
      sortOrder: 0,
      component: { default: eab4e3642af32ca3183a4ba2d4b0482fe }
    },
    e9922f7b6522788416bdd8de4845d8832: {
      id: 'e9922f7b6522788416bdd8de4845d8832',
      sortOrder: 20,
      component: { default: e9922f7b6522788416bdd8de4845d8832 }
    }
  },
  leftColumn: {
    e37bf117a66a32da78e447515d5108d27: {
      id: 'e37bf117a66a32da78e447515d5108d27',
      sortOrder: 1,
      component: { default: e37bf117a66a32da78e447515d5108d27 }
    }
  },
  rightColumn: {
    e00986f8a5ffbca38d1fd0b45b7377f11: {
      id: 'e00986f8a5ffbca38d1fd0b45b7377f11',
      sortOrder: 30,
      component: { default: e00986f8a5ffbca38d1fd0b45b7377f11 }
    },
    e211c2fa2b6fa6913869a7eb5269d0bb7: {
      id: 'e211c2fa2b6fa6913869a7eb5269d0bb7',
      sortOrder: 25,
      component: { default: e211c2fa2b6fa6913869a7eb5269d0bb7 }
    },
    edf92e07655b2fc34cd7a13411d804140: {
      id: 'edf92e07655b2fc34cd7a13411d804140',
      sortOrder: 15,
      component: { default: edf92e07655b2fc34cd7a13411d804140 }
    }
  },
  footer: {
    e1dcb7447781c87e8b5dbcd7126ec93ef: {
      id: 'e1dcb7447781c87e8b5dbcd7126ec93ef',
      sortOrder: 10,
      component: { default: e1dcb7447781c87e8b5dbcd7126ec93ef }
    }
  },
  head: {
    e2b6e20920b7e0cce146f99c500ebc3f9: {
      id: 'e2b6e20920b7e0cce146f99c500ebc3f9',
      sortOrder: 5,
      component: { default: e2b6e20920b7e0cce146f99c500ebc3f9 }
    }
  },
  body: {
    ee0a8efda73779da330342e1f92f72d87: {
      id: 'ee0a8efda73779da330342e1f92f72d87',
      sortOrder: 1,
      component: { default: ee0a8efda73779da330342e1f92f72d87 }
    },
    ed239e378a9b62fdc82ddb5fbf70a1b80: {
      id: 'ed239e378a9b62fdc82ddb5fbf70a1b80',
      sortOrder: 10,
      component: { default: ed239e378a9b62fdc82ddb5fbf70a1b80 }
    },
    e4e223522edabcad19f6b9cbcb3b1746c: {
      id: 'e4e223522edabcad19f6b9cbcb3b1746c',
      sortOrder: 0,
      component: { default: e4e223522edabcad19f6b9cbcb3b1746c }
    }
  },
  header: {
    e1bb3a7d913f332202c6a8d5763b9e8fb: {
      id: 'e1bb3a7d913f332202c6a8d5763b9e8fb',
      sortOrder: 10,
      component: { default: e1bb3a7d913f332202c6a8d5763b9e8fb }
    }
  },
  '*': {
    collection_products: {
      id: 'collection_products',
      sortOrder: 0,
      component: { default: collection_products }
    },
    text_block: {
      id: 'text_block',
      sortOrder: 0,
      component: { default: text_block }
    },
    basic_menu: {
      id: 'basic_menu',
      sortOrder: 0,
      component: { default: basic_menu }
    }
  }
} 