Changelog
=========

All changes in the package are documented in the main repository. See: https://github.com/ckeditor/ckeditor5/blob/master/CHANGELOG.md.

Changes for the past releases are available below.

## [19.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v18.0.0...v19.0.0) (2020-04-29)

Internal changes only (updated dependencies, documentation, etc.).


## [18.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v17.0.0...v18.0.0) (2020-03-19)

Internal changes only (updated dependencies, documentation, etc.).


## [17.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v16.0.0...v17.0.0) (2020-02-18)

### Other changes

* Updated translations. ([b3bc679](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/b3bc679))


## [16.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v15.0.0...v16.0.0) (2019-12-04)

### Other changes

* Updated translations. ([5249150](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/5249150))


## [15.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.5...v15.0.0) (2019-10-23)

### Other changes

* Updated translations. ([b7ae885](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/b7ae885)) ([f03c652](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/f03c652))


## [11.0.5](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.4...v11.0.5) (2019-08-26)

### Other changes

* Changed the URL under bugs key in package.json file. Now we have one issue tracker. See [ckeditor/ckeditor5#1988](https://github.com/ckeditor/ckeditor5/issues/1988). ([7edd73b](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/7edd73b))
* Updated translations. ([8fb4cc2](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/8fb4cc2))


## [11.0.4](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.3...v11.0.4) (2019-07-10)

Internal changes only (updated dependencies, documentation, etc.).


## [11.0.3](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.2...v11.0.3) (2019-07-04)

Internal changes only (updated dependencies, documentation, etc.).


## [11.0.2](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.1...v11.0.2) (2019-06-05)

### Other changes

* Updated translations. ([7d00af1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/7d00af1))


## [11.0.1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v11.0.0...v11.0.1) (2019-04-10)

### Other changes

* Updated translations. ([3a0fe51](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/3a0fe51))


## [11.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v10.0.4...v11.0.0) (2019-02-28)

### Other changes

* Updated translations. ([0e55853](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/0e55853)) ([818eeae](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/818eeae)) ([707fa21](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/707fa21))

### BREAKING CHANGES

* Upgraded minimal versions of Node to `8.0.0` and npm to `5.7.1`. See: [ckeditor/ckeditor5#1507](https://github.com/ckeditor/ckeditor5/issues/1507). ([612ea3c](https://github.com/ckeditor/ckeditor5-cloud-services/commit/612ea3c))


## [10.0.4](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v10.0.3...v10.0.4) (2018-12-05)

Internal changes only (updated dependencies, documentation, etc.).


## [10.0.3](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v10.0.2...v10.0.3) (2018-10-08)

### Other changes

* Updated translations. ([896412d](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/896412d))


## [10.0.2](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v10.0.1...v10.0.2) (2018-07-18)

### Other changes

* Updated translations. ([69db8e3](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/69db8e3))


## [10.0.1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v10.0.0...v10.0.1) (2018-06-21)

### Other changes

* Updated translations. ([6360b78](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/6360b78))


## [10.0.0](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v1.0.0-beta.4...v10.0.0) (2018-04-25)

### Other changes

* Changed the license to GPL2+ only. See [ckeditor/ckeditor5#991](https://github.com/ckeditor/ckeditor5/issues/991). ([06caac5](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/06caac5))

### BREAKING CHANGES

* The license under which CKEditor 5 is released has been changed from a triple GPL, LGPL and MPL license to a GPL2+ only. See [ckeditor/ckeditor5#991](https://github.com/ckeditor/ckeditor5/issues/991) for more information.


## [1.0.0-beta.4](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v1.0.0-beta.2...v1.0.0-beta.4) (2018-04-19)

### Other changes

* Updated translations. ([02712bb](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/02712bb))


## [1.0.0-beta.2](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v1.0.0-beta.1...v1.0.0-beta.2) (2018-04-10)

Internal changes only (updated dependencies, documentation, etc.).


## [1.0.0-beta.1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v1.0.0-alpha.2...v1.0.0-beta.1) (2018-03-15)

Internal changes only (updated dependencies, documentation, etc.).


## [1.0.0-alpha.2](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v1.0.0-alpha.1...v1.0.0-alpha.2) (2017-11-14)

### Other changes

* Updated translations. ([7f80868](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/7f80868))


## [1.0.0-alpha.1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v0.1.1...v1.0.0-alpha.1) (2017-10-03)

### Other changes

* The plugin will not log a warning when `config.ckfinder.uploadUrl` is not specified (because `FileRepository` will do it itself). Closes [#5](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/issues/5). ([1a15688](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/1a15688))


## [0.1.1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/compare/v0.1.0...v0.1.1) (2017-09-03)

### Other changes

* Aligned the implementation to changes in the image upload. ([eb456ac](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/commit/eb456ac))


## 0.1.0 (2017-05-08)

### Features

* Initial implementation. Closes [#1](https://github.com/ckeditor/ckeditor5-adapter-ckfinder/issues/1).
