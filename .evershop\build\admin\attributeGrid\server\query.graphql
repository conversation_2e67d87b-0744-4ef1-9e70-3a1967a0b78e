{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvso7f: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvso7g: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvso7h: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvso7i: url(routeId: \"productGrid\")\n  e3cb8f7smcvvso7j: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvso7k: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvso7l: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvso7m: url(routeId: \"productNew\")\n", "e82ff4a93eb22bfafa5240637f872404d": "\n  e3cb8f7smcvvso7n: attributes(filters: $variable_3cb8f7smcvvso7o) {\n    items {\n      attributeId\n      uuid\n      attributeName\n      attributeCode\n      type\n      isRequired\n      isFilterable\n      editUrl\n      updateApi\n      deleteApi\n      groups {\n        items {\n          attributeGroupId\n          groupName\n          updateApi\n        }\n      }\n    }\n    total\n    currentFilters {\n      key\n      operation\n      value\n    }\n  }\n", "ea564d430dfd857cdd0c3dbdc0bd4bf1b": "", "e087fbd4e4d44853cee7b3d3ea8883df5": "\n  e3cb8f7smcvvso7p: url(routeId: \"attributeNew\")\n", "e8a45f55d1c8a41c32d663bd16a198fbd": "", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvso7q: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvso7r: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvso7s: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvso7t: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvso7u: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvso7v: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvso7w: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvso7x: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvso7y: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvso7z: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvso80: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvso81: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvso82: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvso83: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvso84: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvso85: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvso86: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvso87: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvso88\n    count: $variable_3cb8f7smcvvso89\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvso8a: textWidget(text: $variable_3cb8f7smcvvso8f, className: $variable_3cb8f7smcvvso8g) {\n    text\n    className\n  }\n  e3cb8f7smcvvso8b: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso8c: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso8d: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso8e: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvso8h: basicMenuWidget(settings: $variable_3cb8f7smcvvso8i) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "e82ff4a93eb22bfafa5240637f872404d": {"values": {"variable_3cb8f7smcvvso7o": "getContextValue_J2ZpbHRlcnNGcm9tVXJsJw=="}, "defs": [{"origin": "filters", "type": "[FilterInput]", "alias": "variable_3cb8f7smcvvso7o"}]}, "ea564d430dfd857cdd0c3dbdc0bd4bf1b": {"values": {}, "defs": []}, "e087fbd4e4d44853cee7b3d3ea8883df5": {"values": {}, "defs": []}, "e8a45f55d1c8a41c32d663bd16a198fbd": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvso88": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvso89": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvso88"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvso89"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvso8f": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvso8g": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvso8f"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvso8g"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvso8i": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvso8i"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvso7f"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvso7g"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvso7h"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvso7i"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvso7j"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvso7k"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvso7l"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvso7m"}], "e82ff4a93eb22bfafa5240637f872404d": [{"origin": "attributes", "alias": "e3cb8f7smcvvso7n"}], "ea564d430dfd857cdd0c3dbdc0bd4bf1b": [], "e087fbd4e4d44853cee7b3d3ea8883df5": [{"origin": "newAttributeUrl", "alias": "e3cb8f7smcvvso7p"}], "e8a45f55d1c8a41c32d663bd16a198fbd": [], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvso7q"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvso7r"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvso7s"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso7t"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso7u"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvso7v"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvso7w"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvso7x"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvso7y"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvso7z"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvso80"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvso81"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvso82"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvso83"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvso84"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvso85"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvso86"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvso87"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvso8a"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvso8b"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvso8c"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvso8d"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvso8e"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvso8h"}]}}