{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsnyf: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsnyg: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsnyh: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsnyi: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsnyj: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsnyk: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsnyl: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsnym: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsnyn: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsnyo: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsnyp: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsnyq: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsnyr: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsnys: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsnyt: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsnyu: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsnyv: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsnyw: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsnyx: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsnyy: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsnyz: url(routeId: \"couponNew\")\n", "ebde31f34e25c8d5b97b76295afd1576f": "\n  e3cb8f7smcvvsnz0: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    userCondition {\n      groups\n      emails\n      purchased\n    }\n  }\n  e3cb8f7smcvvsnz1: customerGroups {\n    items {\n      value: customerGroupId\n      name: groupName\n    }\n  }\n", "e69d991ce45f38dadb0ebf8ff38bf1235": "\n  e3cb8f7smcvvsnz2: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    discountType\n    targetProducts {\n      maxQty\n      products {\n        key\n        operator\n        value\n        qty\n      }\n    }\n    buyxGety {\n      sku\n      buyQty\n      getQty\n      maxY\n      discount\n    }\n  }\n", "eb910afb43ceb500c9b19493285d3042f": "\n  e3cb8f7smcvvsnz3: url(routeId: \"couponGrid\")\n", "ed0da6e49bca510c71872cb212400bf26": "\n  e3cb8f7smcvvsnz4: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    coupon\n    status\n    description\n    discountAmount\n    freeShipping\n    startDate {\n      text\n    }\n    endDate {\n      text\n    }\n  }\n", "e1e840cefa7e154c70cfa57142c2d2f99": "\n  e3cb8f7smcvvsnz5: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    condition {\n      orderTotal\n      orderQty\n      requiredProducts {\n        key\n        operator\n        value\n        qty\n      }\n    }\n  }\n", "e8734ccc19365fc01acf69054dcc6064b": "\n  e3cb8f7smcvvsnz6: coupon(id: \"getContextValue_ImNvdXBvbklkIiwgbnVsbA==\") {\n    coupon\n  }\n  e3cb8f7smcvvsnz7: url(routeId: \"couponGrid\")\n", "eac5c90894621f9e4201d1ee21bb0cc27": "\n  e3cb8f7smcvvsnz8: url(routeId: \"createCoupon\")\n  e3cb8f7smcvvsnz9: url(routeId: \"couponGrid\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsnza: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsnzb: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsnzc: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsnzd: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsnze: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsnzf\n    count: $variable_3cb8f7smcvvsnzg\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsnzh: textWidget(text: $variable_3cb8f7smcvvsnzm, className: $variable_3cb8f7smcvvsnzn) {\n    text\n    className\n  }\n  e3cb8f7smcvvsnzi: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnzj: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnzk: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnzl: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsnzo: basicMenuWidget(settings: $variable_3cb8f7smcvvsnzp) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "ebde31f34e25c8d5b97b76295afd1576f": {"values": {}, "defs": []}, "e69d991ce45f38dadb0ebf8ff38bf1235": {"values": {}, "defs": []}, "eb910afb43ceb500c9b19493285d3042f": {"values": {}, "defs": []}, "ed0da6e49bca510c71872cb212400bf26": {"values": {}, "defs": []}, "e1e840cefa7e154c70cfa57142c2d2f99": {"values": {}, "defs": []}, "e8734ccc19365fc01acf69054dcc6064b": {"values": {}, "defs": []}, "eac5c90894621f9e4201d1ee21bb0cc27": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsnzf": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsnzg": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsnzf"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsnzg"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsnzm": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsnzn": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsnzm"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsnzn"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsnzp": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsnzp"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsnyf"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsnyg"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsnyh"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsnyi"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsnyj"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsnyk"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsnyl"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsnym"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsnyn"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsnyo"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsnyp"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnyq"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnyr"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsnys"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsnyt"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsnyu"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsnyv"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsnyw"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsnyx"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsnyy"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsnyz"}], "ebde31f34e25c8d5b97b76295afd1576f": [{"origin": "coupon", "alias": "e3cb8f7smcvvsnz0"}, {"origin": "groups", "alias": "e3cb8f7smcvvsnz1"}], "e69d991ce45f38dadb0ebf8ff38bf1235": [{"origin": "coupon", "alias": "e3cb8f7smcvvsnz2"}], "eb910afb43ceb500c9b19493285d3042f": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsnz3"}], "ed0da6e49bca510c71872cb212400bf26": [{"origin": "coupon", "alias": "e3cb8f7smcvvsnz4"}], "e1e840cefa7e154c70cfa57142c2d2f99": [{"origin": "coupon", "alias": "e3cb8f7smcvvsnz5"}], "e8734ccc19365fc01acf69054dcc6064b": [{"origin": "coupon", "alias": "e3cb8f7smcvvsnz6"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsnz7"}], "eac5c90894621f9e4201d1ee21bb0cc27": [{"origin": "action", "alias": "e3cb8f7smcvvsnz8"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsnz9"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsnza"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsnzb"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsnzc"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsnzd"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsnze"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsnzh"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnzi"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsnzj"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsnzk"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsnzl"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsnzo"}]}}