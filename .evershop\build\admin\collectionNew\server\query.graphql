{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsnvr: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsnvs: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsnvt: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsnvu: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsnvv: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsnvw: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsnvx: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsnvy: url(routeId: \"productNew\")\n", "ee1639b0ee799844472796d76922a5691": "\n  e3cb8f7smcvvsnvz: url(routeId: \"collectionGrid\")\n", "e7621c65da4b96f13a86277b75ed390bc": "\n  e3cb8f7smcvvsnw0: collection(\n    code: \"getContextValue_ImNvbGxlY3Rpb25Db2RlIiwgbnVsbA==\"\n  ) {\n    collectionId\n    name\n    code\n    description\n  }\n  e3cb8f7smcvvsnw1: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnw2: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnw3: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnw4: url(routeId: \"folderCreate\")\n", "eed8a16f2d16dbd5df87f2ba9495038ef": "\n  e3cb8f7smcvvsnw5: collection(\n    code: \"getContextValue_ImNvbGxlY3Rpb25Db2RlIiwgbnVsbA==\"\n  ) {\n    name\n  }\n  e3cb8f7smcvvsnw6: url(routeId: \"collectionGrid\")\n", "e3ed6a4bc2d41b72ec6ef78956750d9e8": "\n  e3cb8f7smcvvsnw7: url(routeId: \"createCollection\")\n  e3cb8f7smcvvsnw8: url(routeId: \"collectionGrid\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsnw9: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsnwa: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsnwb: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsnwc: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsnwd: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsnwe: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsnwf: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsnwg: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsnwh: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsnwi: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsnwj: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsnwk: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsnwl: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsnwm: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsnwn: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsnwo: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsnwp: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsnwq: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsnwr\n    count: $variable_3cb8f7smcvvsnws\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsnwt: textWidget(text: $variable_3cb8f7smcvvsnwy, className: $variable_3cb8f7smcvvsnwz) {\n    text\n    className\n  }\n  e3cb8f7smcvvsnwu: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnwv: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnww: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsnwx: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsnx0: basicMenuWidget(settings: $variable_3cb8f7smcvvsnx1) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "ee1639b0ee799844472796d76922a5691": {"values": {}, "defs": []}, "e7621c65da4b96f13a86277b75ed390bc": {"values": {}, "defs": []}, "eed8a16f2d16dbd5df87f2ba9495038ef": {"values": {}, "defs": []}, "e3ed6a4bc2d41b72ec6ef78956750d9e8": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsnwr": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsnws": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsnwr"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsnws"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsnwy": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsnwz": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsnwy"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsnwz"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsnx1": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsnx1"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsnvr"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsnvs"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsnvt"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsnvu"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsnvv"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsnvw"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsnvx"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsnvy"}], "ee1639b0ee799844472796d76922a5691": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsnvz"}], "e7621c65da4b96f13a86277b75ed390bc": [{"origin": "collection", "alias": "e3cb8f7smcvvsnw0"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnw1"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsnw2"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsnw3"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsnw4"}], "eed8a16f2d16dbd5df87f2ba9495038ef": [{"origin": "collection", "alias": "e3cb8f7smcvvsnw5"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsnw6"}], "e3ed6a4bc2d41b72ec6ef78956750d9e8": [{"origin": "action", "alias": "e3cb8f7smcvvsnw7"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsnw8"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsnw9"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsnwa"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsnwb"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnwc"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsnwd"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsnwe"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsnwf"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsnwg"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsnwh"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsnwi"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsnwj"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsnwk"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsnwl"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsnwm"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsnwn"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsnwo"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsnwp"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsnwq"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsnwt"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsnwu"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsnwv"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsnww"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsnwx"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsnx0"}]}}