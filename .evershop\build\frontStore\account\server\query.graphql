{"queries": {"e9063e121a91d8fcd4205395b2308a655": "\n  e3cb8f7smcvvsope: url(routeId: \"catalogSearch\")\n", "e84f9b67788dd1391f5f95e066add1c5b": "\n  e3cb8f7smcvvsopf: url(routeId: \"cart\")\n  e3cb8f7smcvvsopg: cart(id: \"getContextValue_ImNhcnRJZCIsIG51bGw=\") {\n    totalQty\n  }\n", "eab4e3642af32ca3183a4ba2d4b0482fe": "\n  e3cb8f7smcvvsoph: pageInfo {\n    breadcrumbs {\n      title\n      url\n    }\n  }\n", "e1dcb7447781c87e8b5dbcd7126ec93ef": "\n  e3cb8f7smcvvsopi: themeConfig {\n    copyRight\n  }\n", "e2b6e20920b7e0cce146f99c500ebc3f9": "\n  e3cb8f7smcvvsopj: pageInfo {\n    title\n    description\n  }\n  e3cb8f7smcvvsopk: themeConfig {\n    headTags {\n      metas {\n        name\n        content\n        charSet\n        httpEquiv\n        property\n        itemProp\n        itemType\n        itemID\n        lang\n      }\n      links {\n        rel\n        href\n        sizes\n        type\n        hrefLang\n        media\n        title\n        as\n        crossOrigin\n        integrity\n        referrerPolicy\n      }\n      scripts {\n        src\n        type\n        async\n        defer\n        crossOrigin\n        integrity\n        noModule\n        nonce\n      }\n      base {\n        href\n        target\n      }\n    }\n  }\n", "ee0a8efda73779da330342e1f92f72d87": "", "e1bb3a7d913f332202c6a8d5763b9e8fb": "\n  e3cb8f7smcvvsopl: themeConfig {\n    logo {\n      src\n      alt\n      width\n      height\n    }\n  }\n", "ed239e378a9b62fdc82ddb5fbf70a1b80": "", "e304c98dd638df37464b04ed6279e340f": "\n  e3cb8f7smcvvsopm: currentCustomer {\n    uuid\n    fullName\n    email\n  }\n", "ef024a6fecb4bac2d5903178e742224e2": "\n  e3cb8f7smcvvsopn: currentCustomer {\n    uuid\n    fullName\n    email\n    addresses {\n      uuid\n      fullName\n      address1\n      city\n      postcode\n      country {\n        name\n        code\n      }\n      province {\n        name\n        code\n      }\n      telephone\n      isDefault\n      updateApi\n      deleteApi\n    }\n    addAddressApi\n  }\n  e3cb8f7smcvvsopo: setting {\n    customerAddressSchema\n  }\n", "eb809eaa1184423006c17601ca3216d6e": "\n  e3cb8f7smcvvsopp: url(routeId: \"customerLogoutJson\")\n", "ea35719e6ee991323164d48b794c86df0": "\n  e3cb8f7smcvvsopq: currentCustomer {\n    orders {\n      orderId\n      orderNumber\n      createdAt {\n        text\n      }\n      shipmentStatus {\n        name\n        code\n        badge\n      }\n      paymentStatus {\n        name\n        code\n        badge\n      }\n      grandTotal {\n        value\n        text\n      }\n      items {\n        productName\n        thumbnail\n        productPrice {\n          value\n          text\n        }\n        productSku\n        qty\n      }\n    }\n  }\n", "e1c7d051d98d0b4ad74a74e4272614474": "\n  e3cb8f7smcvvsopr: currentCustomer {\n    uuid\n    fullName\n    email\n  }\n  e3cb8f7smcvvsops: url(routeId: \"account\")\n  e3cb8f7smcvvsopt: url(routeId: \"login\")\n", "e4e223522edabcad19f6b9cbcb3b1746c": "", "e9922f7b6522788416bdd8de4845d8832": "", "e1daa0a3aad8d0424dceec88be2c146d6": "\n  e3cb8f7smcvvsopu: collection(code: $variable_3cb8f7smcvvsopv) {\n    collectionId\n    name\n    products(filters: [{key: \"limit\", operation: eq, value: $variable_3cb8f7smcvvsopw}]) {\n      items {\n        ...Product_3cb8f7smcvvsopx\n      }\n    }\n  }\n", "e1a3daab6a8f11241c38e2dcfa3ada3c8": "\n  e3cb8f7smcvvsopy: textWidget(text: $variable_3cb8f7smcvvsopz, className: $variable_3cb8f7smcvvsoq0) {\n    ...TextBlockWidget_3cb8f7smcvvsoq1\n  }\n", "ed432e0319ca33dda24da92b798e75279": "\n  e3cb8f7smcvvsoq2: basicMenuWidget(settings: $variable_3cb8f7smcvvsoq3) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  fragment Product_3cb8f7smcvvsoq4 on Product {\n    productId\n    name\n    sku\n    price {\n      regular {\n        value\n        text\n      }\n      special {\n        value\n        text\n      }\n    }\n    image {\n      alt\n      url: listing\n    }\n    url\n  }\n\n\n  fragment TextBlockWidget_3cb8f7smcvvsoq5 on TextBlockWidget {\n    text\n    className\n  }\n\n\nfragment Product_3cb8f7smcvvsopx on Product {\n ...Product_3cb8f7smcvvsoq4 \n}\nfragment TextBlockWidget_3cb8f7smcvvsoq1 on TextBlockWidget {\n ...TextBlockWidget_3cb8f7smcvvsoq5 \n}", "variables": {"e9063e121a91d8fcd4205395b2308a655": {"values": {}, "defs": []}, "e84f9b67788dd1391f5f95e066add1c5b": {"values": {}, "defs": []}, "eab4e3642af32ca3183a4ba2d4b0482fe": {"values": {}, "defs": []}, "e1dcb7447781c87e8b5dbcd7126ec93ef": {"values": {}, "defs": []}, "e2b6e20920b7e0cce146f99c500ebc3f9": {"values": {}, "defs": []}, "ee0a8efda73779da330342e1f92f72d87": {"values": {}, "defs": []}, "e1bb3a7d913f332202c6a8d5763b9e8fb": {"values": {}, "defs": []}, "ed239e378a9b62fdc82ddb5fbf70a1b80": {"values": {}, "defs": []}, "e304c98dd638df37464b04ed6279e340f": {"values": {}, "defs": []}, "ef024a6fecb4bac2d5903178e742224e2": {"values": {}, "defs": []}, "eb809eaa1184423006c17601ca3216d6e": {"values": {}, "defs": []}, "ea35719e6ee991323164d48b794c86df0": {"values": {}, "defs": []}, "e1c7d051d98d0b4ad74a74e4272614474": {"values": {}, "defs": []}, "e4e223522edabcad19f6b9cbcb3b1746c": {"values": {}, "defs": []}, "e9922f7b6522788416bdd8de4845d8832": {"values": {}, "defs": []}, "e1daa0a3aad8d0424dceec88be2c146d6": {"values": {"variable_3cb8f7smcvvsopv": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsopw": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsopv"}, {"origin": "count", "type": "ID", "alias": "variable_3cb8f7smcvvsopw"}]}, "e1a3daab6a8f11241c38e2dcfa3ada3c8": {"values": {"variable_3cb8f7smcvvsopz": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsoq0": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsopz"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsoq0"}]}, "ed432e0319ca33dda24da92b798e75279": {"values": {"variable_3cb8f7smcvvsoq3": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsoq3"}]}}, "propsMap": {"e9063e121a91d8fcd4205395b2308a655": [{"origin": "searchPageUrl", "alias": "e3cb8f7smcvvsope"}], "e84f9b67788dd1391f5f95e066add1c5b": [{"origin": "cartUrl", "alias": "e3cb8f7smcvvsopf"}, {"origin": "cart", "alias": "e3cb8f7smcvvsopg"}], "eab4e3642af32ca3183a4ba2d4b0482fe": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsoph"}], "e1dcb7447781c87e8b5dbcd7126ec93ef": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsopi"}], "e2b6e20920b7e0cce146f99c500ebc3f9": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsopj"}, {"origin": "themeConfig", "alias": "e3cb8f7smcvvsopk"}], "ee0a8efda73779da330342e1f92f72d87": [], "e1bb3a7d913f332202c6a8d5763b9e8fb": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsopl"}], "ed239e378a9b62fdc82ddb5fbf70a1b80": [], "e304c98dd638df37464b04ed6279e340f": [{"origin": "account", "alias": "e3cb8f7smcvvsopm"}], "ef024a6fecb4bac2d5903178e742224e2": [{"origin": "account", "alias": "e3cb8f7smcvvsopn"}, {"origin": "setting", "alias": "e3cb8f7smcvvsopo"}], "eb809eaa1184423006c17601ca3216d6e": [{"origin": "logoutUrl", "alias": "e3cb8f7smcvvsopp"}], "ea35719e6ee991323164d48b794c86df0": [{"origin": "customer", "alias": "e3cb8f7smcvvsopq"}], "e1c7d051d98d0b4ad74a74e4272614474": [{"origin": "customer", "alias": "e3cb8f7smcvvsopr"}, {"origin": "accountUrl", "alias": "e3cb8f7smcvvsops"}, {"origin": "loginUrl", "alias": "e3cb8f7smcvvsopt"}], "e4e223522edabcad19f6b9cbcb3b1746c": [], "e9922f7b6522788416bdd8de4845d8832": [], "e1daa0a3aad8d0424dceec88be2c146d6": [{"origin": "collection", "alias": "e3cb8f7smcvvsopu"}], "e1a3daab6a8f11241c38e2dcfa3ada3c8": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsopy"}], "ed432e0319ca33dda24da92b798e75279": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsoq2"}]}}