{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvso6b: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvso6c: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvso6d: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvso6e: url(routeId: \"productGrid\")\n  e3cb8f7smcvvso6f: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvso6g: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvso6h: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvso6i: url(routeId: \"productNew\")\n", "eabe6bc7cd134723cf08d3b02f3e9db05": "\n  e3cb8f7smcvvso6j: categories(filters: $variable_3cb8f7smcvvso6k) {\n    items {\n      categoryId\n      uuid\n      name\n      status\n      includeInNav\n      editUrl\n      deleteApi\n      path {\n        name\n      }\n    }\n    total\n    currentFilters {\n      key\n      operation\n      value\n    }\n  }\n", "eb09ca5d4692a544fa8d434fb3f90a80e": "", "e0c2bae357fe18fc18e31e98d0b2b01cb": "", "eef4ce42607140119e1daf81a5f6063d6": "\n  e3cb8f7smcvvso6l: url(routeId: \"categoryNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvso6m: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvso6n: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvso6o: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvso6p: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvso6q: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvso6r: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvso6s: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvso6t: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvso6u: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvso6v: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvso6w: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvso6x: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvso6y: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvso6z: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvso70: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvso71: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvso72: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvso73: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvso74\n    count: $variable_3cb8f7smcvvso75\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvso76: textWidget(text: $variable_3cb8f7smcvvso7b, className: $variable_3cb8f7smcvvso7c) {\n    text\n    className\n  }\n  e3cb8f7smcvvso77: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso78: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso79: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso7a: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvso7d: basicMenuWidget(settings: $variable_3cb8f7smcvvso7e) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eabe6bc7cd134723cf08d3b02f3e9db05": {"values": {"variable_3cb8f7smcvvso6k": "getContextValue_J2ZpbHRlcnNGcm9tVXJsJw=="}, "defs": [{"origin": "filters", "type": "[FilterInput]", "alias": "variable_3cb8f7smcvvso6k"}]}, "eb09ca5d4692a544fa8d434fb3f90a80e": {"values": {}, "defs": []}, "e0c2bae357fe18fc18e31e98d0b2b01cb": {"values": {}, "defs": []}, "eef4ce42607140119e1daf81a5f6063d6": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvso74": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvso75": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvso74"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvso75"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvso7b": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvso7c": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvso7b"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvso7c"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvso7e": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvso7e"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvso6b"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvso6c"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvso6d"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvso6e"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvso6f"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvso6g"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvso6h"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvso6i"}], "eabe6bc7cd134723cf08d3b02f3e9db05": [{"origin": "categories", "alias": "e3cb8f7smcvvso6j"}], "eb09ca5d4692a544fa8d434fb3f90a80e": [], "e0c2bae357fe18fc18e31e98d0b2b01cb": [], "eef4ce42607140119e1daf81a5f6063d6": [{"origin": "newCateoryUrl", "alias": "e3cb8f7smcvvso6l"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvso6m"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvso6n"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvso6o"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso6p"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso6q"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvso6r"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvso6s"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvso6t"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvso6u"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvso6v"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvso6w"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvso6x"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvso6y"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvso6z"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvso70"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvso71"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvso72"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvso73"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvso76"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvso77"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvso78"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvso79"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvso7a"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvso7d"}]}}