{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvso31: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvso32: url(routeId: \"adminLogout<PERSON><PERSON>\")\n  e3cb8f7smcvvso33: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvso34: url(routeId: \"productGrid\")\n  e3cb8f7smcvvso35: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvso36: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvso37: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvso38: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvso39: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvso3a: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvso3b: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvso3c: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvso3d: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvso3e: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvso3f: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvso3g: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvso3h: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvso3i: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvso3j: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvso3k: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvso3l: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvso3m: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvso3n: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvso3o: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvso3p: url(routeId: \"taxSetting\")\n", "ed4aa280d3204f4c28cde2a4cc3355de2": "\n  e3cb8f7smcvvso3q: url(routeId: \"createTaxClass\")\n  e3cb8f7smcvvso3r: url(routeId: \"saveSetting\")\n  e3cb8f7smcvvso3s: setting {\n    defaultProductTaxClassId\n    defaultShippingTaxClassId\n    baseCalculationAddress\n  }\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvso3t: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvso3u\n    count: $variable_3cb8f7smcvvso3v\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvso3w: textWidget(text: $variable_3cb8f7smcvvso41, className: $variable_3cb8f7smcvvso42) {\n    text\n    className\n  }\n  e3cb8f7smcvvso3x: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso3y: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso3z: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvso40: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvso43: basicMenuWidget(settings: $variable_3cb8f7smcvvso44) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "ed4aa280d3204f4c28cde2a4cc3355de2": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvso3u": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvso3v": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvso3u"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvso3v"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvso41": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvso42": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvso41"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvso42"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvso44": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvso44"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvso31"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvso32"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvso33"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvso34"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvso35"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvso36"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvso37"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvso38"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvso39"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvso3a"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvso3b"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso3c"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvso3d"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvso3e"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvso3f"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvso3g"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvso3h"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvso3i"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvso3j"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvso3k"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvso3l"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvso3m"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvso3n"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvso3o"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvso3p"}], "ed4aa280d3204f4c28cde2a4cc3355de2": [{"origin": "createTaxClassApi", "alias": "e3cb8f7smcvvso3q"}, {"origin": "saveSettingApi", "alias": "e3cb8f7smcvvso3r"}, {"origin": "setting", "alias": "e3cb8f7smcvvso3s"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvso3t"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvso3w"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvso3x"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvso3y"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvso3z"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvso40"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvso43"}]}}