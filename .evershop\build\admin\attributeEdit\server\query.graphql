{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsomt: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsomu: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsomv: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsomw: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsomx: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsomy: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsomz: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvson0: url(routeId: \"productNew\")\n", "ea9811f72c23ff83d2d912e4651f3b223": "\n  e3cb8f7smcvvson1: url(\n    routeId: \"updateAttribute\"\n    params: [{key: \"id\", value: \"getContextValue_ImF0dHJpYnV0ZVV1aWQi\"}]\n  )\n  e3cb8f7smcvvson2: url(routeId: \"attributeGrid\")\n", "ed923de74daf194ce956d327a0d438e31": "\n  e3cb8f7smcvvson3: attribute(id: \"getContextValue_ImF0dHJpYnV0ZUlkIiwgbnVsbA==\") {\n    attributeId\n    isFilterable\n    isRequired\n    displayOnFrontend\n    sortOrder\n  }\n", "e8420adb7c0cabd19dca94609b524f678": "\n  e3cb8f7smcvvson4: url(routeId: \"attributeGrid\")\n", "e5978793f534856b7bc7c409bbfa2796f": "\n  e3cb8f7smcvvson5: attribute(id: \"getContextValue_ImF0dHJpYnV0ZUlkIiwgbnVsbA==\") {\n    attributeId\n    attributeName\n    attributeCode\n    type\n    options {\n      optionId: attributeOptionId\n      uuid\n      optionText\n    }\n    groups {\n      items {\n        value: attributeGroupId\n        label: groupName\n      }\n    }\n  }\n  e3cb8f7smcvvson6: url(routeId: \"createAttributeGroup\")\n", "eaad0ec39178a8f9b87ad013a3231ea13": "\n  e3cb8f7smcvvson7: attribute(id: \"getContextValue_ImF0dHJpYnV0ZUlkIiwgbnVsbA==\") {\n    attributeName\n  }\n  e3cb8f7smcvvson8: url(routeId: \"attributeGrid\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvson9: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsona: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsonb: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsonc: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsond: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsone: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsonf: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsong: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsonh: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsoni: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsonj: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsonk: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsonl: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsonm: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsonn: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsono: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsonp: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsonq: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsonr\n    count: $variable_3cb8f7smcvvsons\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsont: textWidget(text: $variable_3cb8f7smcvvsony, className: $variable_3cb8f7smcvvsonz) {\n    text\n    className\n  }\n  e3cb8f7smcvvsonu: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsonv: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsonw: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsonx: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsoo0: basicMenuWidget(settings: $variable_3cb8f7smcvvsoo1) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "ea9811f72c23ff83d2d912e4651f3b223": {"values": {}, "defs": []}, "ed923de74daf194ce956d327a0d438e31": {"values": {}, "defs": []}, "e8420adb7c0cabd19dca94609b524f678": {"values": {}, "defs": []}, "e5978793f534856b7bc7c409bbfa2796f": {"values": {}, "defs": []}, "eaad0ec39178a8f9b87ad013a3231ea13": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsonr": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsons": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsonr"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsons"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsony": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsonz": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsony"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsonz"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsoo1": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsoo1"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsomt"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsomu"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsomv"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsomw"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsomx"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsomy"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsomz"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvson0"}], "ea9811f72c23ff83d2d912e4651f3b223": [{"origin": "action", "alias": "e3cb8f7smcvvson1"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvson2"}], "ed923de74daf194ce956d327a0d438e31": [{"origin": "attribute", "alias": "e3cb8f7smcvvson3"}], "e8420adb7c0cabd19dca94609b524f678": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvson4"}], "e5978793f534856b7bc7c409bbfa2796f": [{"origin": "attribute", "alias": "e3cb8f7smcvvson5"}, {"origin": "createGroupApi", "alias": "e3cb8f7smcvvson6"}], "eaad0ec39178a8f9b87ad013a3231ea13": [{"origin": "attribute", "alias": "e3cb8f7smcvvson7"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvson8"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvson9"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsona"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsonb"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsonc"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsond"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsone"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsonf"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsong"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsonh"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsoni"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsonj"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsonk"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsonl"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsonm"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsonn"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsono"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsonp"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsonq"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsont"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsonu"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsonv"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsonw"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsonx"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsoo0"}]}}