
      import React from 'react';
      import ReactDOM from 'react-dom';
      import { Area } from '@evershop/evershop/components/common';
      import {HydrateFrontStore} from '@evershop/evershop/components/common';
      
import e9063e121a91d8fcd4205395b2308a655 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/catalog/pages/frontStore/all/SearchBox.js';
import e84f9b67788dd1391f5f95e066add1c5b from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/all/MiniCart.js';
import e2e52044be19c12752a47ae49abe09905 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/Checkout.js';
import e3a1eb6baaf1da26585886b92e8f57845 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/PaymentStep.js';
import eab508db42d14518a9ac5e041927389cb from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/ShipmentStep.js';
import ee22bc7a04e07dd8c87ede3a57e599a5e from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/ShippingMethods.js';
import e51da6ef57a1cfc9bd58523981b0177a2 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/ShippingNote.js';
import e6b975045a82f78a585e73549bae58c36 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/Summary.js';
import e256c314c558d7f8a6bbd0de93cb963c6 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/checkout/pages/frontStore/checkout/SummaryMobile.js';
import eab4e3642af32ca3183a4ba2d4b0482fe from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Breadcrumb.js';
import e1dcb7447781c87e8b5dbcd7126ec93ef from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Footer.js';
import e2b6e20920b7e0cce146f99c500ebc3f9 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/HeadTags.js';
import ee0a8efda73779da330342e1f92f72d87 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Layout.js';
import e1bb3a7d913f332202c6a8d5763b9e8fb from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Logo.js';
import ed239e378a9b62fdc82ddb5fbf70a1b80 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cms/pages/frontStore/all/Notification.js';
import e9f846bdafafa4eafe1e32cb9ffc37cee from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/cod/pages/frontStore/checkout/CashOnDelivery.js';
import e1c7d051d98d0b4ad74a74e4272614474 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/customer/pages/frontStore/all/UserIcon.js';
import ec37c4d85b86be44d2a7d37ea0dab5da8 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/customer/pages/frontStore/checkout/CustomerInfoStep.js';
import e4e7bc60e939d468b3721456a76862ba3 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/paypal/pages/frontStore/checkout/Paypal.js';
import eb18e41a2897fd4049db6620bd736c4f6 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/modules/stripe/pages/frontStore/checkout/Stripe.js';
import e4e223522edabcad19f6b9cbcb3b1746c from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/extensions/sample/dist/pages/frontStore/all/FreeShippingMessage.js';
import e9922f7b6522788416bdd8de4845d8832 from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/themes/sample/dist/pages/all/EveryWhere.js';
import collection_products from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/CollectionProducts.js';
import text_block from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/TextBlock.js';
import basic_menu from 'file:///C:/Users/<USER>/Desktop/NextJS/glow-254/node_modules/@evershop/evershop/dist/components/frontStore/widgets/BasicMenu.js';
Area.defaultProps.components = {
  'icon-wrapper': {
    e9063e121a91d8fcd4205395b2308a655: {
      id: 'e9063e121a91d8fcd4205395b2308a655',
      sortOrder: 5,
      component: { default: e9063e121a91d8fcd4205395b2308a655 }
    },
    e84f9b67788dd1391f5f95e066add1c5b: {
      id: 'e84f9b67788dd1391f5f95e066add1c5b',
      sortOrder: 10,
      component: { default: e84f9b67788dd1391f5f95e066add1c5b }
    },
    e1c7d051d98d0b4ad74a74e4272614474: {
      id: 'e1c7d051d98d0b4ad74a74e4272614474',
      sortOrder: 30,
      component: { default: e1c7d051d98d0b4ad74a74e4272614474 }
    }
  },
  content: {
    e2e52044be19c12752a47ae49abe09905: {
      id: 'e2e52044be19c12752a47ae49abe09905',
      sortOrder: 10,
      component: { default: e2e52044be19c12752a47ae49abe09905 }
    },
    eab4e3642af32ca3183a4ba2d4b0482fe: {
      id: 'eab4e3642af32ca3183a4ba2d4b0482fe',
      sortOrder: 0,
      component: { default: eab4e3642af32ca3183a4ba2d4b0482fe }
    },
    e9922f7b6522788416bdd8de4845d8832: {
      id: 'e9922f7b6522788416bdd8de4845d8832',
      sortOrder: 20,
      component: { default: e9922f7b6522788416bdd8de4845d8832 }
    }
  },
  checkoutSteps: {
    e3a1eb6baaf1da26585886b92e8f57845: {
      id: 'e3a1eb6baaf1da26585886b92e8f57845',
      sortOrder: 20,
      component: { default: e3a1eb6baaf1da26585886b92e8f57845 }
    },
    eab508db42d14518a9ac5e041927389cb: {
      id: 'eab508db42d14518a9ac5e041927389cb',
      sortOrder: 15,
      component: { default: eab508db42d14518a9ac5e041927389cb }
    },
    ec37c4d85b86be44d2a7d37ea0dab5da8: {
      id: 'ec37c4d85b86be44d2a7d37ea0dab5da8',
      sortOrder: 10,
      component: { default: ec37c4d85b86be44d2a7d37ea0dab5da8 }
    }
  },
  checkoutShippingAddressForm: {
    ee22bc7a04e07dd8c87ede3a57e599a5e: {
      id: 'ee22bc7a04e07dd8c87ede3a57e599a5e',
      sortOrder: 60,
      component: { default: ee22bc7a04e07dd8c87ede3a57e599a5e }
    }
  },
  checkoutSummary: {
    e51da6ef57a1cfc9bd58523981b0177a2: {
      id: 'e51da6ef57a1cfc9bd58523981b0177a2',
      sortOrder: 50,
      component: { default: e51da6ef57a1cfc9bd58523981b0177a2 }
    }
  },
  checkoutPageRight: {
    e6b975045a82f78a585e73549bae58c36: {
      id: 'e6b975045a82f78a585e73549bae58c36',
      sortOrder: 10,
      component: { default: e6b975045a82f78a585e73549bae58c36 }
    }
  },
  beforePlaceOrderButton: {
    e256c314c558d7f8a6bbd0de93cb963c6: {
      id: 'e256c314c558d7f8a6bbd0de93cb963c6',
      sortOrder: 10,
      component: { default: e256c314c558d7f8a6bbd0de93cb963c6 }
    }
  },
  footer: {
    e1dcb7447781c87e8b5dbcd7126ec93ef: {
      id: 'e1dcb7447781c87e8b5dbcd7126ec93ef',
      sortOrder: 10,
      component: { default: e1dcb7447781c87e8b5dbcd7126ec93ef }
    }
  },
  head: {
    e2b6e20920b7e0cce146f99c500ebc3f9: {
      id: 'e2b6e20920b7e0cce146f99c500ebc3f9',
      sortOrder: 5,
      component: { default: e2b6e20920b7e0cce146f99c500ebc3f9 }
    }
  },
  body: {
    ee0a8efda73779da330342e1f92f72d87: {
      id: 'ee0a8efda73779da330342e1f92f72d87',
      sortOrder: 1,
      component: { default: ee0a8efda73779da330342e1f92f72d87 }
    },
    ed239e378a9b62fdc82ddb5fbf70a1b80: {
      id: 'ed239e378a9b62fdc82ddb5fbf70a1b80',
      sortOrder: 10,
      component: { default: ed239e378a9b62fdc82ddb5fbf70a1b80 }
    },
    e4e223522edabcad19f6b9cbcb3b1746c: {
      id: 'e4e223522edabcad19f6b9cbcb3b1746c',
      sortOrder: 0,
      component: { default: e4e223522edabcad19f6b9cbcb3b1746c }
    }
  },
  header: {
    e1bb3a7d913f332202c6a8d5763b9e8fb: {
      id: 'e1bb3a7d913f332202c6a8d5763b9e8fb',
      sortOrder: 10,
      component: { default: e1bb3a7d913f332202c6a8d5763b9e8fb }
    }
  },
  checkoutPaymentMethodcod: {
    e9f846bdafafa4eafe1e32cb9ffc37cee: {
      id: 'e9f846bdafafa4eafe1e32cb9ffc37cee',
      sortOrder: 10,
      component: { default: e9f846bdafafa4eafe1e32cb9ffc37cee }
    }
  },
  checkoutPaymentMethodpaypal: {
    e4e7bc60e939d468b3721456a76862ba3: {
      id: 'e4e7bc60e939d468b3721456a76862ba3',
      sortOrder: 10,
      component: { default: e4e7bc60e939d468b3721456a76862ba3 }
    }
  },
  checkoutPaymentMethodstripe: {
    eb18e41a2897fd4049db6620bd736c4f6: {
      id: 'eb18e41a2897fd4049db6620bd736c4f6',
      sortOrder: 10,
      component: { default: eb18e41a2897fd4049db6620bd736c4f6 }
    }
  },
  '*': {
    collection_products: {
      id: 'collection_products',
      sortOrder: 0,
      component: { default: collection_products }
    },
    text_block: {
      id: 'text_block',
      sortOrder: 0,
      component: { default: text_block }
    },
    basic_menu: {
      id: 'basic_menu',
      sortOrder: 0,
      component: { default: basic_menu }
    }
  }
} 
ReactDOM.hydrate(
        React.createElement(HydrateFrontStore, null),
        document.getElementById('app')
      );