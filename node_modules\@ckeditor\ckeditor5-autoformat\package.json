{"name": "@ckeditor/ckeditor5-autoformat", "version": "36.0.1", "description": "Autoformatting feature for CKEditor 5.", "keywords": ["ckeditor", "ckeditor5", "ckeditor 5", "ckeditor5-feature", "ckeditor5-plugin", "ckeditor5-dll"], "main": "src/index.js", "dependencies": {"ckeditor5": "^36.0.1"}, "devDependencies": {"@ckeditor/ckeditor5-basic-styles": "^36.0.1", "@ckeditor/ckeditor5-block-quote": "^36.0.1", "@ckeditor/ckeditor5-code-block": "^36.0.1", "@ckeditor/ckeditor5-core": "^36.0.1", "@ckeditor/ckeditor5-dev-utils": "^32.0.0", "@ckeditor/ckeditor5-editor-classic": "^36.0.1", "@ckeditor/ckeditor5-engine": "^36.0.1", "@ckeditor/ckeditor5-enter": "^36.0.1", "@ckeditor/ckeditor5-heading": "^36.0.1", "@ckeditor/ckeditor5-horizontal-line": "^36.0.1", "@ckeditor/ckeditor5-list": "^36.0.1", "@ckeditor/ckeditor5-paragraph": "^36.0.1", "@ckeditor/ckeditor5-theme-lark": "^36.0.1", "@ckeditor/ckeditor5-typing": "^36.0.1", "@ckeditor/ckeditor5-undo": "^36.0.1", "typescript": "^4.8.4", "webpack": "^5.58.1", "webpack-cli": "^4.9.0"}, "engines": {"node": ">=14.0.0", "npm": ">=5.7.1"}, "author": "CKSource (http://cksource.com/)", "license": "GPL-2.0-or-later", "homepage": "https://ckeditor.com/ckeditor-5", "bugs": "https://github.com/ckeditor/ckeditor5/issues", "repository": {"type": "git", "url": "https://github.com/ckeditor/ckeditor5.git", "directory": "packages/ckeditor5-autoformat"}, "files": ["lang", "src/**/*.js", "src/**/*.d.ts", "theme", "build", "ckeditor5-metadata.json", "CHANGELOG.md"], "depcheckIgnore": ["eslint-plugin-ckeditor5-rules"], "scripts": {"dll:build": "webpack", "build": "tsc -p ./tsconfig.release.json", "postversion": "npm run build"}}