{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsp5w: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsp5x: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsp5y: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsp5z: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsp60: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsp61: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsp62: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsp63: url(routeId: \"productNew\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsp64: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsp65: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsp66: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsp67: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsp68: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsp69: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsp6a: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsp6b: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsp6c: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsp6d: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsp6e: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsp6f: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsp6g: url(routeId: \"couponNew\")\n", "ebb248aea68d24dc2c5e2ca0cfa0fcb31": "\n  e3cb8f7smcvvsp6h: url(\n    routeId: \"updateCoupon\"\n    params: [{key: \"id\", value: \"getContextValue_ImNvdXBvblV1aWQi\"}]\n  )\n  e3cb8f7smcvvsp6i: url(routeId: \"couponGrid\")\n", "ebde31f34e25c8d5b97b76295afd1576f": "\n  e3cb8f7smcvvsp6j: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    userCondition {\n      groups\n      emails\n      purchased\n    }\n  }\n  e3cb8f7smcvvsp6k: customerGroups {\n    items {\n      value: customerGroupId\n      name: groupName\n    }\n  }\n", "e69d991ce45f38dadb0ebf8ff38bf1235": "\n  e3cb8f7smcvvsp6l: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    discountType\n    targetProducts {\n      maxQty\n      products {\n        key\n        operator\n        value\n        qty\n      }\n    }\n    buyxGety {\n      sku\n      buyQty\n      getQty\n      maxY\n      discount\n    }\n  }\n", "eb910afb43ceb500c9b19493285d3042f": "\n  e3cb8f7smcvvsp6m: url(routeId: \"couponGrid\")\n", "ed0da6e49bca510c71872cb212400bf26": "\n  e3cb8f7smcvvsp6n: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    coupon\n    status\n    description\n    discountAmount\n    freeShipping\n    startDate {\n      text\n    }\n    endDate {\n      text\n    }\n  }\n", "e1e840cefa7e154c70cfa57142c2d2f99": "\n  e3cb8f7smcvvsp6o: coupon(id: \"getContextValue_J2NvdXBvbklkJywgbnVsbA==\") {\n    condition {\n      orderTotal\n      orderQty\n      requiredProducts {\n        key\n        operator\n        value\n        qty\n      }\n    }\n  }\n", "e8734ccc19365fc01acf69054dcc6064b": "\n  e3cb8f7smcvvsp6p: coupon(id: \"getContextValue_ImNvdXBvbklkIiwgbnVsbA==\") {\n    coupon\n  }\n  e3cb8f7smcvvsp6q: url(routeId: \"couponGrid\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsp6r: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsp6s: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsp6t: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsp6u: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsp6v: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsp6w\n    count: $variable_3cb8f7smcvvsp6x\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsp6y: textWidget(text: $variable_3cb8f7smcvvsp73, className: $variable_3cb8f7smcvvsp74) {\n    text\n    className\n  }\n  e3cb8f7smcvvsp6z: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp70: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp71: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsp72: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsp75: basicMenuWidget(settings: $variable_3cb8f7smcvvsp76) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "ebb248aea68d24dc2c5e2ca0cfa0fcb31": {"values": {}, "defs": []}, "ebde31f34e25c8d5b97b76295afd1576f": {"values": {}, "defs": []}, "e69d991ce45f38dadb0ebf8ff38bf1235": {"values": {}, "defs": []}, "eb910afb43ceb500c9b19493285d3042f": {"values": {}, "defs": []}, "ed0da6e49bca510c71872cb212400bf26": {"values": {}, "defs": []}, "e1e840cefa7e154c70cfa57142c2d2f99": {"values": {}, "defs": []}, "e8734ccc19365fc01acf69054dcc6064b": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsp6w": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsp6x": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsp6w"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsp6x"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsp73": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsp74": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsp73"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsp74"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsp76": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsp76"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsp5w"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsp5x"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsp5y"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsp5z"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsp60"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsp61"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsp62"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsp63"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsp64"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsp65"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsp66"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsp67"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsp68"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsp69"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsp6a"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsp6b"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsp6c"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsp6d"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsp6e"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsp6f"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsp6g"}], "ebb248aea68d24dc2c5e2ca0cfa0fcb31": [{"origin": "action", "alias": "e3cb8f7smcvvsp6h"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsp6i"}], "ebde31f34e25c8d5b97b76295afd1576f": [{"origin": "coupon", "alias": "e3cb8f7smcvvsp6j"}, {"origin": "groups", "alias": "e3cb8f7smcvvsp6k"}], "e69d991ce45f38dadb0ebf8ff38bf1235": [{"origin": "coupon", "alias": "e3cb8f7smcvvsp6l"}], "eb910afb43ceb500c9b19493285d3042f": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsp6m"}], "ed0da6e49bca510c71872cb212400bf26": [{"origin": "coupon", "alias": "e3cb8f7smcvvsp6n"}], "e1e840cefa7e154c70cfa57142c2d2f99": [{"origin": "coupon", "alias": "e3cb8f7smcvvsp6o"}], "e8734ccc19365fc01acf69054dcc6064b": [{"origin": "coupon", "alias": "e3cb8f7smcvvsp6p"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsp6q"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsp6r"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsp6s"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsp6t"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsp6u"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsp6v"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsp6y"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsp6z"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsp70"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsp71"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsp72"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsp75"}]}}