{"queries": {"ec70c22df5f099bdfcebe397859226054": "\n  e3cb8f7smcvvsovd: currentAdminUser {\n    adminUserId\n    fullName\n    email\n  }\n  e3cb8f7smcvvsove: url(routeId: \"adminLogout<PERSON>son\")\n  e3cb8f7smcvvsovf: url(routeId: \"adminLogin\")\n", "e97b100f517bff553aae97dd31d7f9249": "\n  e3cb8f7smcvvsovg: url(routeId: \"productGrid\")\n  e3cb8f7smcvvsovh: url(routeId: \"categoryGrid\")\n  e3cb8f7smcvvsovi: url(routeId: \"attributeGrid\")\n  e3cb8f7smcvvsovj: url(routeId: \"collectionGrid\")\n", "e04b8121a7250abb640811c2476af1150": "\n  e3cb8f7smcvvsovk: url(routeId: \"productNew\")\n", "ed6dce7629263b3c8e418f2fdcfdadfac": "\n  e3cb8f7smcvvsovl: url(\n    routeId: \"updateCollection\"\n    params: [{key: \"id\", value: \"getContextValue_ImNvbGxlY3Rpb25VdWlkIg==\"}]\n  )\n  e3cb8f7smcvvsovm: url(routeId: \"collectionGrid\")\n", "e98ecdc0a1a5c71ea6eaa13136dd4a980": "\n  e3cb8f7smcvvsovn: collection(\n    code: \"getContextValue_ImNvbGxlY3Rpb25Db2RlIiwgbnVsbA==\"\n  ) {\n    collectionId\n    code\n    addProductApi: addProductUrl\n  }\n", "ee1639b0ee799844472796d76922a5691": "\n  e3cb8f7smcvvsovo: url(routeId: \"collectionGrid\")\n", "e7621c65da4b96f13a86277b75ed390bc": "\n  e3cb8f7smcvvsovp: collection(\n    code: \"getContextValue_ImNvbGxlY3Rpb25Db2RlIiwgbnVsbA==\"\n  ) {\n    collectionId\n    name\n    code\n    description\n  }\n  e3cb8f7smcvvsovq: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsovr: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsovs: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsovt: url(routeId: \"folderCreate\")\n", "eed8a16f2d16dbd5df87f2ba9495038ef": "\n  e3cb8f7smcvvsovu: collection(\n    code: \"getContextValue_ImNvbGxlY3Rpb25Db2RlIiwgbnVsbA==\"\n  ) {\n    name\n  }\n  e3cb8f7smcvvsovv: url(routeId: \"collectionGrid\")\n", "eb76f74151daf1b93f9edbe45b86f26e5": "\n  e3cb8f7smcvvsovw: url(routeId: \"shippingSetting\")\n", "e5d7dc0c8c76e45451e795f8d743fb527": "\n  e3cb8f7smcvvsovx: url(routeId: \"cmsPageGrid\")\n  e3cb8f7smcvvsovy: url(routeId: \"widgetGrid\")\n", "e08490b3c06c2de7f3c690854f0a1bed6": "\n  e3cb8f7smcvvsovz: themeConfig {\n    copyRight\n  }\n", "eb1a516897eae4329796fdfd9457bf2a2": "", "e03bcf91a570563c8710aaeff437938d1": "\n  e3cb8f7smcvvsow0: themeConfig {\n    logo {\n      src\n      alt\n    }\n  }\n  e3cb8f7smcvvsow1: url(routeId: \"dashboard\")\n", "ee6d31560871c3e93cb8b02d93e5c9ed2": "\n  e3cb8f7smcvvsow2: pageInfo {\n    title\n    description\n  }\n", "e8f7ea183df71b7a7285cc0a8dc8f6b60": "", "e7e3bfab6f7542419f684826a014797ee": "", "e4a518579666d48f424ac549955b8600d": "\n  e3cb8f7smcvvsow3: url(routeId: \"dashboard\")\n", "e7891d52a003753856feaed4d441a5125": "", "e9381802a22771a312927abfa56eb1f44": "\n  e3cb8f7smcvvsow4: version\n", "e0cb3843d9b393dccd23ba72e26af5d76": "\n  e3cb8f7smcvvsow5: url(routeId: \"customerGrid\")\n", "e210d82a6f26d6c09230a430f6d53658a": "\n  e3cb8f7smcvvsow6: url(routeId: \"orderGrid\")\n", "e76e8f55114fbd34dd521f9dacfc320f5": "\n  e3cb8f7smcvvsow7: url(routeId: \"couponGrid\")\n", "e649537ca6fafb59dffcf59fcd8446016": "\n  e3cb8f7smcvvsow8: url(routeId: \"couponNew\")\n", "e903864b09f1dd86f890772de34a5bacd": "\n  e3cb8f7smcvvsow9: url(routeId: \"paymentSetting\")\n", "e6b8adbe0216888a342a04f37bd1f6624": "\n  e3cb8f7smcvvsowa: url(routeId: \"storeSetting\")\n", "e0297bc82affd261ba0c504e82a2cdd2f": "\n  e3cb8f7smcvvsowb: url(routeId: \"storeSetting\")\n", "e7836577ca064eb03926afeba2a889b93": "\n  e3cb8f7smcvvsowc: url(routeId: \"taxSetting\")\n", "e361d24f0917afaaeb65f3f4088fb8348": "", "e4329040ecf9c0065cd10119f8fbaffd3": "\n  e3cb8f7smcvvsowd: collectionProductsWidget(\n    collection: $variable_3cb8f7smcvvsowe\n    count: $variable_3cb8f7smcvvsowf\n  ) {\n    collection\n    count\n  }\n", "e484e14383ee5a7fd55b51af124c0ecad": "\n  e3cb8f7smcvvsowg: textWidget(text: $variable_3cb8f7smcvvsowl, className: $variable_3cb8f7smcvvsowm) {\n    text\n    className\n  }\n  e3cb8f7smcvvsowh: url(routeId: \"fileBrowser\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsowi: url(routeId: \"fileDelete\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsowj: url(routeId: \"imageUpload\", params: [{key: \"0\", value: \"\"}])\n  e3cb8f7smcvvsowk: url(routeId: \"folderCreate\")\n", "e2c8c913b98c5038c810261e3590f75d1": "\n  e3cb8f7smcvvsown: basicMenuWidget(settings: $variable_3cb8f7smcvvsowo) {\n    menus {\n      id\n      name\n      url\n      type\n      uuid\n      children {\n        id\n        name\n        url\n        type\n        uuid\n      }\n    }\n    isMain\n    className\n  }\n"}, "fragments": "\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "variables": {"ec70c22df5f099bdfcebe397859226054": {"values": {}, "defs": []}, "e97b100f517bff553aae97dd31d7f9249": {"values": {}, "defs": []}, "e04b8121a7250abb640811c2476af1150": {"values": {}, "defs": []}, "ed6dce7629263b3c8e418f2fdcfdadfac": {"values": {}, "defs": []}, "e98ecdc0a1a5c71ea6eaa13136dd4a980": {"values": {}, "defs": []}, "ee1639b0ee799844472796d76922a5691": {"values": {}, "defs": []}, "e7621c65da4b96f13a86277b75ed390bc": {"values": {}, "defs": []}, "eed8a16f2d16dbd5df87f2ba9495038ef": {"values": {}, "defs": []}, "eb76f74151daf1b93f9edbe45b86f26e5": {"values": {}, "defs": []}, "e5d7dc0c8c76e45451e795f8d743fb527": {"values": {}, "defs": []}, "e08490b3c06c2de7f3c690854f0a1bed6": {"values": {}, "defs": []}, "eb1a516897eae4329796fdfd9457bf2a2": {"values": {}, "defs": []}, "e03bcf91a570563c8710aaeff437938d1": {"values": {}, "defs": []}, "ee6d31560871c3e93cb8b02d93e5c9ed2": {"values": {}, "defs": []}, "e8f7ea183df71b7a7285cc0a8dc8f6b60": {"values": {}, "defs": []}, "e7e3bfab6f7542419f684826a014797ee": {"values": {}, "defs": []}, "e4a518579666d48f424ac549955b8600d": {"values": {}, "defs": []}, "e7891d52a003753856feaed4d441a5125": {"values": {}, "defs": []}, "e9381802a22771a312927abfa56eb1f44": {"values": {}, "defs": []}, "e0cb3843d9b393dccd23ba72e26af5d76": {"values": {}, "defs": []}, "e210d82a6f26d6c09230a430f6d53658a": {"values": {}, "defs": []}, "e76e8f55114fbd34dd521f9dacfc320f5": {"values": {}, "defs": []}, "e649537ca6fafb59dffcf59fcd8446016": {"values": {}, "defs": []}, "e903864b09f1dd86f890772de34a5bacd": {"values": {}, "defs": []}, "e6b8adbe0216888a342a04f37bd1f6624": {"values": {}, "defs": []}, "e0297bc82affd261ba0c504e82a2cdd2f": {"values": {}, "defs": []}, "e7836577ca064eb03926afeba2a889b93": {"values": {}, "defs": []}, "e361d24f0917afaaeb65f3f4088fb8348": {"values": {}, "defs": []}, "e4329040ecf9c0065cd10119f8fbaffd3": {"values": {"variable_3cb8f7smcvvsowe": "getWidgetSetting_ImNvbGxlY3Rpb24i", "variable_3cb8f7smcvvsowf": "getWidgetSetting_ImNvdW50Ig=="}, "defs": [{"origin": "collection", "type": "String", "alias": "variable_3cb8f7smcvvsowe"}, {"origin": "count", "type": "Int", "alias": "variable_3cb8f7smcvvsowf"}]}, "e484e14383ee5a7fd55b51af124c0ecad": {"values": {"variable_3cb8f7smcvvsowl": "getWidgetSetting_InRleHQi", "variable_3cb8f7smcvvsowm": "getWidgetSetting_ImNsYXNzTmFtZSI="}, "defs": [{"origin": "text", "type": "String", "alias": "variable_3cb8f7smcvvsowl"}, {"origin": "className", "type": "String", "alias": "variable_3cb8f7smcvvsowm"}]}, "e2c8c913b98c5038c810261e3590f75d1": {"values": {"variable_3cb8f7smcvvsowo": "getWidgetSetting_"}, "defs": [{"origin": "settings", "type": "JSON", "alias": "variable_3cb8f7smcvvsowo"}]}}, "propsMap": {"ec70c22df5f099bdfcebe397859226054": [{"origin": "adminUser", "alias": "e3cb8f7smcvvsovd"}, {"origin": "logoutUrl", "alias": "e3cb8f7smcvvsove"}, {"origin": "loginPage", "alias": "e3cb8f7smcvvsovf"}], "e97b100f517bff553aae97dd31d7f9249": [{"origin": "productGrid", "alias": "e3cb8f7smcvvsovg"}, {"origin": "categoryGrid", "alias": "e3cb8f7smcvvsovh"}, {"origin": "attributeGrid", "alias": "e3cb8f7smcvvsovi"}, {"origin": "collectionGrid", "alias": "e3cb8f7smcvvsovj"}], "e04b8121a7250abb640811c2476af1150": [{"origin": "productNew", "alias": "e3cb8f7smcvvsovk"}], "ed6dce7629263b3c8e418f2fdcfdadfac": [{"origin": "action", "alias": "e3cb8f7smcvvsovl"}, {"origin": "gridUrl", "alias": "e3cb8f7smcvvsovm"}], "e98ecdc0a1a5c71ea6eaa13136dd4a980": [{"origin": "collection", "alias": "e3cb8f7smcvvsovn"}], "ee1639b0ee799844472796d76922a5691": [{"origin": "gridUrl", "alias": "e3cb8f7smcvvsovo"}], "e7621c65da4b96f13a86277b75ed390bc": [{"origin": "collection", "alias": "e3cb8f7smcvvsovp"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsovq"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsovr"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsovs"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsovt"}], "eed8a16f2d16dbd5df87f2ba9495038ef": [{"origin": "collection", "alias": "e3cb8f7smcvvsovu"}, {"origin": "backUrl", "alias": "e3cb8f7smcvvsovv"}], "eb76f74151daf1b93f9edbe45b86f26e5": [{"origin": "shippingSettingUrl", "alias": "e3cb8f7smcvvsovw"}], "e5d7dc0c8c76e45451e795f8d743fb527": [{"origin": "cmsPageGrid", "alias": "e3cb8f7smcvvsovx"}, {"origin": "widgetGrid", "alias": "e3cb8f7smcvvsovy"}], "e08490b3c06c2de7f3c690854f0a1bed6": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsovz"}], "eb1a516897eae4329796fdfd9457bf2a2": [], "e03bcf91a570563c8710aaeff437938d1": [{"origin": "themeConfig", "alias": "e3cb8f7smcvvsow0"}, {"origin": "dashboardUrl", "alias": "e3cb8f7smcvvsow1"}], "ee6d31560871c3e93cb8b02d93e5c9ed2": [{"origin": "pageInfo", "alias": "e3cb8f7smcvvsow2"}], "e8f7ea183df71b7a7285cc0a8dc8f6b60": [], "e7e3bfab6f7542419f684826a014797ee": [], "e4a518579666d48f424ac549955b8600d": [{"origin": "dashboard", "alias": "e3cb8f7smcvvsow3"}], "e7891d52a003753856feaed4d441a5125": [], "e9381802a22771a312927abfa56eb1f44": [{"origin": "version", "alias": "e3cb8f7smcvvsow4"}], "e0cb3843d9b393dccd23ba72e26af5d76": [{"origin": "customerGrid", "alias": "e3cb8f7smcvvsow5"}], "e210d82a6f26d6c09230a430f6d53658a": [{"origin": "orderGrid", "alias": "e3cb8f7smcvvsow6"}], "e76e8f55114fbd34dd521f9dacfc320f5": [{"origin": "couponGrid", "alias": "e3cb8f7smcvvsow7"}], "e649537ca6fafb59dffcf59fcd8446016": [{"origin": "couponNew", "alias": "e3cb8f7smcvvsow8"}], "e903864b09f1dd86f890772de34a5bacd": [{"origin": "paymentSettingUrl", "alias": "e3cb8f7smcvvsow9"}], "e6b8adbe0216888a342a04f37bd1f6624": [{"origin": "storeSetting", "alias": "e3cb8f7smcvvsowa"}], "e0297bc82affd261ba0c504e82a2cdd2f": [{"origin": "storeSettingUrl", "alias": "e3cb8f7smcvvsowb"}], "e7836577ca064eb03926afeba2a889b93": [{"origin": "taxSettingUrl", "alias": "e3cb8f7smcvvsowc"}], "e361d24f0917afaaeb65f3f4088fb8348": [], "e4329040ecf9c0065cd10119f8fbaffd3": [{"origin": "collectionProductsWidget", "alias": "e3cb8f7smcvvsowd"}], "e484e14383ee5a7fd55b51af124c0ecad": [{"origin": "textWidget", "alias": "e3cb8f7smcvvsowg"}, {"origin": "browserApi", "alias": "e3cb8f7smcvvsowh"}, {"origin": "deleteApi", "alias": "e3cb8f7smcvvsowi"}, {"origin": "uploadApi", "alias": "e3cb8f7smcvvsowj"}, {"origin": "folderCreateApi", "alias": "e3cb8f7smcvvsowk"}], "e2c8c913b98c5038c810261e3590f75d1": [{"origin": "basicMenuWidget", "alias": "e3cb8f7smcvvsown"}]}}